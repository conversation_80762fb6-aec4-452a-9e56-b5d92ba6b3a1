<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Giỏ Hàng</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body onload="displayCart()">
    <header>
      <h1>Giỏ Hàng</h1>
      <nav>
        <a href="index.html">Trang Chủ</a>
        <a href="product.html">Sản Phẩm</a>
        <a href="cart.html">Giỏ Hàng</a>
        <a href="checkout.html"><PERSON><PERSON> Toán</a>
        <a href="login.html"><PERSON><PERSON><PERSON></a>
        <a href="register.html"><PERSON><PERSON><PERSON></a>
      </nav>
    </header>
    <main>
      <table>
        <thead>
          <tr>
            <th>Sản phẩm</th>
            <th>Giá</th>
            <th><PERSON><PERSON> lượng</th>
            <th>Tổng</th>
            <th><PERSON><PERSON> t<PERSON></th>
          </tr>
        </thead>
        <tbody id="cart-items"></tbody>
      </table>
      <h3>Tổng cộng: <span id="cart-total">0₫</span></h3>
      <div style="text-align: right; margin-top: var(--spacing-lg)">
        <a
          href="checkout.html"
          style="
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 600;
          "
        >
          Tiến hành thanh toán
        </a>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>Về Chúng Tôi</h3>
          <p>
            Cửa hàng online cung cấp các sản phẩm thời trang chất lượng cao với
            giá cả hợp lý.
          </p>
          <p>
            Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách
            hàng.
          </p>
        </div>

        <div class="footer-section">
          <h3>Liên Hệ</h3>
          <p>Email: <EMAIL></p>
          <p>Điện thoại: 0123 456 789</p>
          <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
        </div>

        <div class="footer-section">
          <h3>Liên Kết Nhanh</h3>
          <a href="index.html">Trang Chủ</a>
          <a href="product.html">Sản Phẩm</a>
          <a href="cart.html">Giỏ Hàng</a>
          <a href="login.html">Đăng Nhập</a>
          <a href="register.html">Đăng Ký</a>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2023 Cửa Hàng Online. Tất cả quyền được bảo lưu.</p>
      </div>
    </footer>

    <script src="main.js"></script>
    <script>
      // Thêm hàm showNotification để xử lý thông báo
      if (typeof showNotification !== "function") {
        function showNotification(message) {
          // Kiểm tra xem đã có thông báo nào chưa
          const existingNotification = document.querySelector(".notification");
          if (existingNotification) {
            existingNotification.remove();
          }

          // Tạo thông báo mới
          const notification = document.createElement("div");
          notification.className = "notification";
          notification.innerHTML = `
            <div class="notification-content">
              <span>${message}</span>
              <button class="close-btn">&times;</button>
            </div>
          `;

          // Thêm style cho thông báo
          notification.style.position = "fixed";
          notification.style.top = "20px";
          notification.style.right = "20px";
          notification.style.backgroundColor = "var(--success-color)";
          notification.style.color = "white";
          notification.style.padding = "15px 20px";
          notification.style.borderRadius = "var(--border-radius)";
          notification.style.boxShadow = "var(--box-shadow)";
          notification.style.zIndex = "1000";
          notification.style.transition = "all 0.3s ease";
          notification.style.opacity = "0";

          // Thêm style cho nút đóng
          const closeBtn = notification.querySelector(".close-btn");
          closeBtn.style.background = "none";
          closeBtn.style.border = "none";
          closeBtn.style.color = "white";
          closeBtn.style.fontSize = "20px";
          closeBtn.style.cursor = "pointer";
          closeBtn.style.marginLeft = "10px";

          // Thêm thông báo vào body
          document.body.appendChild(notification);

          // Hiệu ứng hiển thị
          setTimeout(() => {
            notification.style.opacity = "1";
          }, 10);

          // Đóng thông báo khi click vào nút đóng
          closeBtn.addEventListener("click", () => {
            notification.style.opacity = "0";
            setTimeout(() => {
              notification.remove();
            }, 300);
          });

          // Tự động đóng sau 3 giây
          setTimeout(() => {
            notification.style.opacity = "0";
            setTimeout(() => {
              notification.remove();
            }, 300);
          }, 3000);
        }
      }

      // Hiển thị giỏ hàng
      displayCart();
    </script>
  </body>
</html>
