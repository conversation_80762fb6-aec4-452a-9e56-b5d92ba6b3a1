<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thông tin sản phẩm</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <header>
      <h1><PERSON><PERSON> sách sản phẩm</h1>
      <nav>
        <a href="index.html">Trang Chủ</a>
        <a href="product.html">Sản Phẩm</a>
        <a href="cart.html">Giỏ Hàng</a>
        <a href="login.html">Đ<PERSON>ng <PERSON>ập</a>
        <a href="register.html"><PERSON><PERSON><PERSON></a>
      </nav>
    </header>
    <main>
      <div class="product-grid">
        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=800&auto=format&fit=crop"
            alt="Áo thun nam"
          />
          <h3><PERSON><PERSON> thun nam</h3>
          <p>Chất liệu cotton 100%, thoáng mát, size S, M, L.</p>
          <div class="product-price">
            <span class="current-price">200,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo thun nam', 200000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2?q=80&w=800&auto=format&fit=crop"
            alt="Giày thể thao"
          />
          <h3>Giày thể thao</h3>
          <p>Thiết kế thời trang, êm chân, size 39-44.</p>
          <div class="product-price">
            <span class="current-price">500,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Giày thể thao', 500000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <img
            src="https://images.unsplash.com/photo-1542272604-787c3835535d?q=80&w=800&auto=format&fit=crop"
            alt="Quần jeans"
          />
          <h3>Quần jeans</h3>
          <p>Chất liệu denim bền bỉ, phong cách trẻ trung.</p>
          <div class="product-price">
            <span class="current-price">350,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Quần jeans', 350000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1591047139829-d91aecb6caea?q=80&w=800&auto=format&fit=crop"
            alt="Áo khoác dù"
          />
          <h3>Áo khoác dù</h3>
          <p>Chống nước, phù hợp đi mưa và gió.</p>
          <div class="product-price">
            <span class="current-price">400,000₫</span>
            <span class="old-price">480,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo khoác dù', 400000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1524805444758-089113d48a6d?q=80&w=800&auto=format&fit=crop"
            alt="Đồng hồ thể thao"
          />
          <h3>Đồng hồ thể thao</h3>
          <p>Chống nước, bấm giờ, dây cao su.</p>
          <div class="product-price">
            <span class="current-price">600,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Đồng hồ thể thao', 600000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <img
            src="https://images.unsplash.com/photo-1611312449408-fcece27cdbb7?q=80&w=800&auto=format&fit=crop"
            alt="Mũ lưỡi trai"
          />
          <h3>Mũ lưỡi trai</h3>
          <p>Chất liệu cotton, đa dạng màu sắc.</p>
          <div class="product-price">
            <span class="current-price">150,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Mũ lưỡi trai', 150000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1560769629-975ec94e6a86?q=80&w=800&auto=format&fit=crop"
            alt="Túi xách nữ"
          />
          <h3>Túi xách nữ</h3>
          <p>Chất liệu da PU, thiết kế sang trọng.</p>
          <div class="product-price">
            <span class="current-price">700,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Túi xách nữ', 700000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?q=80&w=800&auto=format&fit=crop"
            alt="Váy đầm"
          />
          <h3>Váy đầm</h3>
          <p>Chất liệu voan, thoáng mát, kiểu dáng trẻ trung.</p>
          <div class="product-price">
            <span class="current-price">450,000₫</span>
            <span class="old-price">520,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Váy đầm', 450000)">Thêm vào giỏ</button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <img
            src="https://images.unsplash.com/photo-1617038260897-41a1f14a8ca0?q=80&w=800&auto=format&fit=crop"
            alt="Kính mát"
          />
          <h3>Kính mát</h3>
          <p>Chống tia UV, kiểu dáng thời trang.</p>
          <div class="product-price">
            <span class="current-price">300,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Kính mát', 300000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
            alt="Giày sandal"
          />
          <h3>Giày sandal</h3>
          <p>Đế cao su, thoải mái khi đi lại.</p>
          <div class="product-price">
            <span class="current-price">250,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Giày sandal', 250000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105?q=80&w=800&auto=format&fit=crop"
            alt="Áo blazer nữ"
          />
          <h3>Áo blazer nữ</h3>
          <p>Thiết kế thanh lịch, phù hợp công sở.</p>
          <div class="product-price">
            <span class="current-price">650,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo blazer nữ', 650000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1571945153237-4929e783af4a?q=80&w=800&auto=format&fit=crop"
            alt="Quần short jean"
          />
          <h3>Quần short jean</h3>
          <p>Chất liệu denim cao cấp, phong cách trẻ trung.</p>
          <div class="product-price">
            <span class="current-price">280,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Quần short jean', 280000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?q=80&w=800&auto=format&fit=crop"
            alt="Áo len cổ lọ"
          />
          <h3>Áo len cổ lọ</h3>
          <p>Chất liệu len mềm mại, giữ ấm tốt.</p>
          <div class="product-price">
            <span class="current-price">480,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo len cổ lọ', 480000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=800&auto=format&fit=crop"
            alt="Giày sneaker trắng"
          />
          <h3>Giày sneaker trắng</h3>
          <p>Thiết kế hiện đại, phù hợp mọi phong cách.</p>
          <div class="product-price">
            <span class="current-price">750,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Giày sneaker trắng', 750000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?q=80&w=800&auto=format&fit=crop"
            alt="Áo polo nam"
          />
          <h3>Áo polo nam</h3>
          <p>Chất liệu cotton cao cấp, thoáng mát.</p>
          <div class="product-price">
            <span class="current-price">350,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo polo nam', 350000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?q=80&w=800&auto=format&fit=crop"
            alt="Váy công sở"
          />
          <h3>Váy công sở</h3>
          <p>Thiết kế thanh lịch, phù hợp môi trường công sở.</p>
          <div class="product-price">
            <span class="current-price">520,000₫</span>
            <span class="old-price">650,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Váy công sở', 520000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>Về Chúng Tôi</h3>
          <p>
            Cửa hàng online cung cấp các sản phẩm thời trang chất lượng cao với
            giá cả hợp lý.
          </p>
          <p>
            Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách
            hàng.
          </p>
        </div>

        <div class="footer-section">
          <h3>Liên Hệ</h3>
          <p>Email: <EMAIL></p>
          <p>Điện thoại: 0123 456 789</p>
          <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
        </div>

        <div class="footer-section">
          <h3>Liên Kết Nhanh</h3>
          <a href="index.html">Trang Chủ</a>
          <a href="product.html">Sản Phẩm</a>
          <a href="cart.html">Giỏ Hàng</a>
          <a href="login.html">Đăng Nhập</a>
          <a href="register.html">Đăng Ký</a>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2023 Cửa Hàng Online. Tất cả quyền được bảo lưu.</p>
      </div>
    </footer>

    <script src="main.js"></script>
    <script>
      // Thêm hàm showNotification để xử lý thông báo khi thêm sản phẩm vào giỏ hàng
      if (typeof showNotification !== "function") {
        function showNotification(message) {
          // Kiểm tra xem đã có thông báo nào chưa
          const existingNotification = document.querySelector(".notification");
          if (existingNotification) {
            existingNotification.remove();
          }

          // Tạo thông báo mới
          const notification = document.createElement("div");
          notification.className = "notification";
          notification.innerHTML = `
            <div class="notification-content">
              <span>${message}</span>
              <button class="close-btn">&times;</button>
            </div>
          `;

          // Thêm style cho thông báo
          notification.style.position = "fixed";
          notification.style.top = "20px";
          notification.style.right = "20px";
          notification.style.backgroundColor = "var(--success-color)";
          notification.style.color = "white";
          notification.style.padding = "15px 20px";
          notification.style.borderRadius = "var(--border-radius)";
          notification.style.boxShadow = "var(--box-shadow)";
          notification.style.zIndex = "1000";
          notification.style.transition = "all 0.3s ease";
          notification.style.opacity = "0";

          // Thêm style cho nút đóng
          const closeBtn = notification.querySelector(".close-btn");
          closeBtn.style.background = "none";
          closeBtn.style.border = "none";
          closeBtn.style.color = "white";
          closeBtn.style.fontSize = "20px";
          closeBtn.style.cursor = "pointer";
          closeBtn.style.marginLeft = "10px";

          // Thêm thông báo vào body
          document.body.appendChild(notification);

          // Hiệu ứng hiển thị
          setTimeout(() => {
            notification.style.opacity = "1";
          }, 10);

          // Đóng thông báo khi click vào nút đóng
          closeBtn.addEventListener("click", () => {
            notification.style.opacity = "0";
            setTimeout(() => {
              notification.remove();
            }, 300);
          });

          // Tự động đóng sau 3 giây
          setTimeout(() => {
            notification.style.opacity = "0";
            setTimeout(() => {
              notification.remove();
            }, 300);
          }, 3000);
        }
      }

      // Thêm sự kiện cho nút wishlist
      document.querySelectorAll(".wishlist-btn").forEach((button) => {
        button.addEventListener("click", function (e) {
          e.preventDefault();

          // Toggle heart fill/empty
          if (this.textContent === "❤") {
            this.textContent = "♥";
            this.style.backgroundColor = "var(--secondary-color)";
            this.style.color = "white";
            showNotification("Đã thêm vào danh sách yêu thích");
          } else {
            this.textContent = "❤";
            this.style.backgroundColor = "var(--bg-dark)";
            this.style.color = "var(--text-color)";
            showNotification("Đã xóa khỏi danh sách yêu thích");
          }
        });
      });
    </script>
  </body>
</html>
