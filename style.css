/* CSS Variables for consistent colors and styling */
:root {
  --primary-color: #3a86ff;
  --primary-dark: #2667cc;
  --secondary-color: #ff006e;
  --accent-color: #ffbe0b;
  --success-color: #27ae60;
  --success-dark: #2d8a00;
  --text-color: #333333;
  --text-light: #666666;
  --bg-color: #f8f9fa;
  --bg-light: #ffffff;
  --bg-dark: #e9ecef;
  --border-color: #dee2e6;
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

/* Reset CSS */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Base Typography */
body {
  font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  font-size: 16px;
}

/* Promotion Banner */
.promo-banner {
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--primary-color)
  );
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  text-align: center;
  position: relative;
  font-size: 0.9rem;
  font-weight: 600;
  animation: slideDown 0.5s ease;
}

.promo-banner .close-btn {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.8;
  transition: var(--transition);
}

.promo-banner .close-btn:hover {
  opacity: 1;
}

.promo-banner.hidden {
  display: none;
}

/* Header Styling */
header {
  background-color: var(--bg-light);
  color: var(--text-color);
  padding: var(--spacing-lg) var(--spacing-xl);
  text-align: center;
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

header h1 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
  font-weight: 700;
}

/* Navigation */
nav {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

nav a {
  color: var(--text-color);
  text-decoration: none;
  padding: var(--spacing-xs) var(--spacing-md);
  font-weight: 600;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

nav a:hover {
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
}

/* Main Content */
main {
  max-width: 1200px;
  margin: var(--spacing-xl) auto;
  padding: 0 var(--spacing-lg);
}

/* Banner/Slider */
.banner-slider {
  position: relative;
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  height: 400px;
}

.banner-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.banner-slide.active {
  opacity: 1;
}

.banner-content {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: var(--spacing-lg) var(--spacing-xl);
  max-width: 500px;
  margin-left: var(--spacing-xl);
  border-radius: var(--border-radius);
}

.banner-content h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.banner-content p {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

.banner-content .btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.banner-content .btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.slider-controls {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.slider-dot {
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
}

.slider-dot.active {
  background-color: white;
}

/* Store Introduction */
.store-intro {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.store-intro h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.8rem;
}

.store-intro p {
  color: var(--text-light);
  max-width: 800px;
  margin: 0 auto var(--spacing-md);
  line-height: 1.7;
}

.store-features {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 250px;
}

.feature i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.feature h3 {
  margin-bottom: var(--spacing-xs);
  font-size: 1.2rem;
  color: var(--text-color);
}

.feature p {
  text-align: center;
  font-size: 0.9rem;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.section-header h2 {
  color: var(--primary-color);
  font-size: 1.5rem;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.view-all {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.view-all:hover {
  color: var(--primary-dark);
}

.view-all i {
  margin-left: var(--spacing-xs);
}

/* Product Categories */
.product-categories {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  overflow-x: auto;
  padding-bottom: var(--spacing-sm);
  scrollbar-width: thin;
}

.product-categories::-webkit-scrollbar {
  height: 6px;
}

.product-categories::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 10px;
}

.category-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  border-radius: 30px;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.category-btn:hover,
.category-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Product Grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* Ensure grid works well with 5-6 products per row on large screens */
@media (min-width: 1400px) {
  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (min-width: 1600px) {
  .product-grid {
    grid-template-columns: repeat(6, 1fr);
    max-width: 1400px;
    margin: 0 auto var(--spacing-xl);
  }
}

/* Product Card */
.product {
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.product:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.badge-new {
  background-color: var(--primary-color);
  color: white;
}

.badge-sale {
  background-color: var(--secondary-color);
  color: white;
}

.badge-hot {
  background-color: var(--accent-color);
  color: var(--text-color);
}

.product img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  margin-bottom: var(--spacing-md);
  border-radius: calc(var(--border-radius) - 2px);
  transition: var(--transition);
  background-color: var(--bg-dark);
}

.product:hover img {
  transform: scale(1.05);
}

.product h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.product h3 a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.product h3 a:hover {
  color: var(--primary-dark);
}

.product-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.current-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.old-price {
  font-size: 0.9rem;
  color: var(--text-light);
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: auto;
}

.product-actions button {
  flex: 1;
  background-color: var(--success-color);
  border: none;
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.95rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-actions .wishlist-btn {
  flex: 0 0 40px;
  background-color: var(--bg-dark);
  color: var(--text-color);
}

.product-actions .wishlist-btn:hover {
  background-color: var(--secondary-color);
  color: white;
}

.product-actions button:hover {
  background-color: var(--success-dark);
}

/* Shopping Cart Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--spacing-xl);
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

table,
th,
td {
  border: 1px solid var(--border-color);
}

th,
td {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
}

th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

td button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

td button:hover {
  opacity: 0.9;
}

h3 {
  margin-top: var(--spacing-xl);
  text-align: right;
  font-size: 1.4rem;
  color: var(--text-color);
}

/* Checkout Form */
form {
  max-width: 500px;
  margin: var(--spacing-xl) auto;
  background-color: var(--bg-light);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

form input {
  width: 100%;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 134, 255, 0.2);
}

form button {
  width: 100%;
  background-color: var(--primary-color);
  border: none;
  padding: var(--spacing-md);
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 600;
}

form button:hover {
  background-color: var(--primary-dark);
}

/* Footer Styling */
footer {
  background-color: var(--bg-light);
  color: var(--text-color);
  padding: var(--spacing-xl) var(--spacing-lg);
  margin-top: var(--spacing-xl);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-section h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
  text-align: left;
}

.footer-section p,
.footer-section a {
  color: var(--text-light);
  margin-bottom: var(--spacing-sm);
  font-size: 0.95rem;
}

.footer-section a {
  display: block;
  text-decoration: none;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  header {
    padding: var(--spacing-md);
  }

  nav {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .banner-slider {
    height: 300px;
  }

  .banner-content {
    max-width: 80%;
    margin-left: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .banner-content h2 {
    font-size: 1.5rem;
  }

  .banner-content p {
    font-size: 1rem;
  }

  .store-intro {
    padding: var(--spacing-lg);
  }

  .store-features {
    gap: var(--spacing-lg);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-md);
  }

  .product img {
    height: 180px;
  }

  form {
    padding: var(--spacing-lg);
  }

  table,
  th,
  td {
    font-size: 0.9rem;
  }

  th,
  td {
    padding: var(--spacing-sm);
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  body {
    font-size: 14px;
  }

  header {
    padding: var(--spacing-sm);
  }

  header h1 {
    font-size: 1.5rem;
  }

  nav {
    gap: var(--spacing-xs);
  }

  nav a {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.9rem;
  }

  .banner-slider {
    height: 200px;
    margin-bottom: var(--spacing-md);
  }

  .banner-content {
    max-width: 90%;
    margin-left: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .banner-content h2 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
  }

  .banner-content p {
    font-size: 0.85rem;
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .banner-content .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.85rem;
  }

  .slider-controls {
    bottom: var(--spacing-sm);
  }

  .slider-dot {
    width: 8px;
    height: 8px;
  }

  .store-intro {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .store-intro h2 {
    font-size: 1.4rem;
  }

  .store-intro p {
    font-size: 0.9rem;
  }

  .store-features {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
  }

  .feature {
    max-width: 100%;
  }

  .section-header h2 {
    font-size: 1.2rem;
  }

  .product-categories {
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
  }

  .category-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.85rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .product {
    padding: var(--spacing-md);
  }

  .product img {
    height: 180px;
  }

  .product h3 {
    font-size: 1.1rem;
  }

  .product-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .product-actions .wishlist-btn {
    flex: 1;
  }

  main {
    padding: 0 var(--spacing-md);
    margin: var(--spacing-md) auto;
  }

  form {
    padding: var(--spacing-md);
  }

  table {
    display: block;
    overflow-x: auto;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .footer-section {
    margin-bottom: var(--spacing-md);
  }

  .footer-section a {
    display: inline-block;
    margin: 0 var(--spacing-xs) var(--spacing-xs) 0;
  }
}

/* Marketing Features */

/* Pop-up Modal */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.popup-overlay.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  max-width: 500px;
  width: 90%;
  position: relative;
  text-align: center;
  transform: scale(0.7);
  transition: transform 0.3s ease;
}

.popup-overlay.show .popup-content {
  transform: scale(1);
}

.popup-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.popup-close:hover {
  color: var(--text-color);
}

.popup-title {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
}

.popup-discount {
  background: var(--secondary-color);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.popup-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.popup-actions button {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.popup-actions .btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.popup-actions .btn-secondary {
  background-color: var(--bg-dark);
  color: var(--text-color);
}

.popup-actions button:hover {
  transform: translateY(-2px);
}

/* Countdown Timer */
.countdown-section {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: var(--spacing-xl);
  text-align: center;
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.countdown-title {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.countdown-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  min-width: 80px;
}

.countdown-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.countdown-label {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: var(--spacing-xs);
}

/* Testimonials */
.testimonials-section {
  background-color: var(--bg-light);
  padding: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.testimonials-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.testimonial {
  text-align: center;
  padding: var(--spacing-lg);
  display: none;
}

.testimonial.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.testimonial-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto var(--spacing-md);
  object-fit: cover;
}

.testimonial-rating {
  color: #ffd700;
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
}

.testimonial-text {
  font-style: italic;
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-color);
}

.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--border-color);
  cursor: pointer;
  transition: var(--transition);
}

.testimonial-dot.active {
  background-color: var(--primary-color);
}

/* Newsletter */
.newsletter-section {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: var(--spacing-xl);
  text-align: center;
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.newsletter-title {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.newsletter-subtitle {
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.newsletter-form {
  display: flex;
  max-width: 400px;
  margin: 0 auto;
  gap: var(--spacing-sm);
}

.newsletter-input {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.newsletter-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.newsletter-btn:hover {
  background-color: #e55039;
  transform: translateY(-2px);
}

/* Social Proof */
.social-proof {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  max-width: 300px;
  z-index: 500;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.social-proof.show {
  transform: translateX(0);
}

.social-proof-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.social-proof-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.social-proof-text {
  flex: 1;
  font-size: 0.9rem;
}

.social-proof-name {
  font-weight: 600;
  color: var(--primary-color);
}

.social-proof-action {
  color: var(--text-light);
}

/* Enhanced Animations */
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%,
  43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px var(--primary-color);
  }
  50% {
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wheelSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1800deg);
  }
}

@keyframes cardFlip {
  0% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(-90deg);
  }
  100% {
    transform: rotateY(-180deg);
  }
}

@keyframes confetti {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(720deg);
    opacity: 0;
  }
}

/* Mini Games Styles */

/* Game Button */
.games-button {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: 50px;
  padding: var(--spacing-md) var(--spacing-lg);
  font-weight: 600;
  cursor: pointer;
  box-shadow: var(--box-shadow);
  z-index: 999;
  transition: var(--transition);
  animation: pulse 2s infinite;
}

.games-button:hover {
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 8px 25px rgba(58, 134, 255, 0.3);
}

.games-button i {
  margin-right: var(--spacing-xs);
}

/* Game Modal */
.game-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.game-modal.show {
  opacity: 1;
  visibility: visible;
}

.game-modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  transform: scale(0.7);
  transition: transform 0.3s ease;
}

.game-modal.show .game-modal-content {
  transform: scale(1);
}

.game-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.game-close:hover {
  color: var(--text-color);
}

/* Game Selection */
.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.game-card {
  background: var(--bg-light);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.game-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.game-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.game-card:hover::before {
  left: 100%;
}

.game-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.game-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.game-description {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
}

.game-status {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.game-status.available {
  background-color: var(--success-color);
  color: white;
}

.game-status.played {
  background-color: var(--text-light);
  color: white;
}

.game-status.locked {
  background-color: var(--border-color);
  color: var(--text-light);
}

/* Lucky Wheel */
.lucky-wheel-container {
  text-align: center;
  padding: var(--spacing-lg);
}

.wheel-container {
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto var(--spacing-lg);
}

.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 8px solid var(--primary-color);
  position: relative;
  overflow: hidden;
  transition: transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
}

.wheel.spinning {
  animation: wheelSpin 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.wheel-segment {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
}

.wheel-pointer {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 30px solid var(--secondary-color);
  z-index: 10;
}

.spin-button {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: 50px;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.spin-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(58, 134, 255, 0.3);
}

.spin-button:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Scratch Card */
.scratch-card-container {
  text-align: center;
  padding: var(--spacing-lg);
}

.scratch-card {
  position: relative;
  width: 300px;
  height: 200px;
  margin: 0 auto var(--spacing-lg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.scratch-card-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--accent-color)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
}

.scratch-card-front {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #c0c0c0, #808080);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  cursor: crosshair;
}

/* Daily Check-in */
.checkin-container {
  padding: var(--spacing-lg);
}

.checkin-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.checkin-day {
  aspect-ratio: 1;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: var(--bg-light);
}

.checkin-day.checked {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.checkin-day.today {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  animation: pulse 1s infinite;
}

.checkin-day.future {
  opacity: 0.5;
  cursor: not-allowed;
}

.day-number {
  font-weight: 600;
  font-size: 1.1rem;
}

.day-reward {
  font-size: 0.7rem;
  margin-top: 2px;
}

.checkin-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-dark);
  border-radius: var(--border-radius);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Memory Game */
.memory-game-container {
  padding: var(--spacing-lg);
}

.memory-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-sm);
  max-width: 400px;
  margin: 0 auto var(--spacing-lg);
}

.memory-card {
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  cursor: pointer;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.memory-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.memory-card.flipped {
  transform: rotateY(180deg);
}

.memory-card.matched {
  opacity: 0.6;
  transform: rotateY(180deg) scale(0.9);
}

.memory-card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.memory-card-front {
  background: var(--primary-color);
  color: white;
}

.memory-card-back {
  background: var(--bg-light);
  border: 2px solid var(--border-color);
  transform: rotateY(180deg);
}

.memory-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-dark);
  border-radius: var(--border-radius);
}

.memory-stat {
  text-align: center;
}

.memory-stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.memory-stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Responsive for marketing features */
@media (max-width: 768px) {
  .countdown-timer {
    gap: var(--spacing-md);
  }

  .countdown-item {
    min-width: 60px;
    padding: var(--spacing-sm);
  }

  .countdown-number {
    font-size: 1.5rem;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .popup-content {
    padding: var(--spacing-lg);
  }

  .popup-actions {
    flex-direction: column;
  }

  .social-proof {
    left: 10px;
    right: 10px;
    max-width: none;
  }

  /* Games responsive */
  .games-button {
    right: 10px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
  }

  .games-grid {
    grid-template-columns: 1fr;
  }

  .wheel-container {
    width: 250px;
    height: 250px;
  }

  .scratch-card {
    width: 250px;
    height: 150px;
  }

  .memory-grid {
    max-width: 300px;
  }

  .checkin-calendar {
    gap: var(--spacing-xs);
  }
}

/* Loading States and Progress Indicators */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--bg-dark);
  border-radius: 4px;
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 50px 50px;
  animation: move 2s linear infinite;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 50px 50px;
  }
}

/* Achievement System */
.achievement-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  animation: bounce 0.6s ease;
}

.achievement-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  z-index: 1002;
  animation: achievementPop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes achievementPop {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Confetti Effect */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1001;
}

.confetti-piece {
  position: absolute;
  width: 10px;
  height: 10px;
  background: var(--primary-color);
  animation: confetti 3s linear infinite;
}

.confetti-piece:nth-child(odd) {
  background: var(--secondary-color);
  border-radius: 50%;
}

.confetti-piece:nth-child(3n) {
  background: var(--accent-color);
  width: 6px;
  height: 6px;
}

/* Tutorial Overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1003;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-content {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  max-width: 500px;
  width: 90%;
  text-align: center;
  position: relative;
}

.tutorial-step {
  display: none;
}

.tutorial-step.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.tutorial-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
}

/* Sound Control */
.sound-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.sound-toggle:hover {
  background: white;
  transform: scale(1.1);
}

.sound-toggle.muted {
  opacity: 0.5;
}

/* Difficulty Selector */
.difficulty-selector {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  justify-content: center;
}

.difficulty-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  background: var(--bg-light);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
}

.difficulty-btn.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.difficulty-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Combo System */
.combo-display {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--accent-color)
  );
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-weight: 600;
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.combo-display.active {
  transform: scale(1);
  animation: pulse 0.6s ease;
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible {
  outline: 3px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .game-card {
    border: 3px solid var(--text-color);
  }

  .wheel-segment {
    border: 2px solid var(--text-color);
  }

  .memory-card-face {
    border: 2px solid var(--text-color);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .wheel {
    transition: none;
  }

  .memory-card {
    transition: none;
  }
}
