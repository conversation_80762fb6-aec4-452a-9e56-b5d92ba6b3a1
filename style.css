/* ========================================================================== */
/* CSS VARIABLES (CUSTOM PROPERTIES) - Hệ thống biến CSS toàn cục           */
/* ========================================================================== */

:root {
  /* M<PERSON>u sắc chính của thương hiệu */
  --primary-color: #3a86ff; /* Màu xanh chính - dùng cho buttons, links, highlights */
  --primary-dark: #2667cc; /* Màu xanh đậm - dùng cho hover states */
  --secondary-color: #ff006e; /* <PERSON><PERSON><PERSON> hồng phụ - dùng cho accents và sale badges */
  --accent-color: #ffbe0b; /* <PERSON><PERSON><PERSON> vàng nhấn - dùng cho hot badges, notifications */

  /* <PERSON><PERSON>u sắc trạng thái */
  --success-color: #27ae60; /* Màu xanh lá - thành công, add to cart buttons */
  --success-dark: #2d8a00; /* Màu xanh lá đậm - hover state cho success buttons */

  /* Màu sắc cho text và typography */
  --text-color: #333333; /* Màu text chính - đen nhạt để dễ đọc và accessibility */
  --text-light: #666666; /* Màu text phụ - xám nhạt cho descriptions, captions */

  /* Màu sắc background và layout */
  --bg-color: #f8f9fa; /* Màu nền chính của trang - xám rất nhạt */
  --bg-light: #ffffff; /* Màu nền sáng - trắng cho cards, modals */
  --bg-dark: #e9ecef; /* Màu nền đậm - xám nhạt cho borders, dividers */
  --border-color: #dee2e6; /* Màu viền chuẩn - xám nhạt cho tất cả borders */

  /* Hệ thống bo góc nhất quán */
  --border-radius: 8px; /* Bo góc chuẩn - dùng cho buttons, inputs, cards */
  --border-radius-lg: 12px; /* Bo góc lớn - dùng cho modals, major components */

  /* Hệ thống đổ bóng */
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Bóng nhẹ - cho cards, buttons */

  /* Hiệu ứng chuyển đổi */
  --transition: all 0.3s ease; /* Transition chuẩn - mượt mà cho hover effects */

  /* Hệ thống khoảng cách sử dụng rem để responsive */
  --spacing-xs: 0.25rem; /* 4px - khoảng cách rất nhỏ */
  --spacing-sm: 0.5rem; /* 8px - khoảng cách nhỏ */
  --spacing-md: 1rem; /* 16px - khoảng cách trung bình (base) */
  --spacing-lg: 1.5rem; /* 24px - khoảng cách lớn */
  --spacing-xl: 2rem; /* 32px - khoảng cách rất lớn */
}

/* ========================================================================== */
/* CSS RESET - Loại bỏ default styling của browser để có control hoàn toàn   */
/* ========================================================================== */

*,
*::before,
*::after {
  margin: 0; /* Loại bỏ margin mặc định của tất cả elements */
  padding: 0; /* Loại bỏ padding mặc định của tất cả elements */
  box-sizing: border-box; /* Border-box giúp tính toán size dễ dàng hơn */
}

/* ========================================================================== */
/* BASE TYPOGRAPHY - Thiết lập typography cơ bản cho toàn trang              */
/* ========================================================================== */

body {
  /* Font stack ưu tiên system fonts để load nhanh và native feel */
  font-family: "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--bg-color); /* Sử dụng biến CSS cho consistency */
  color: var(--text-color); /* Màu text chính từ CSS variables */
  line-height: 1.6; /* Line height 1.6 tối ưu cho readability */
  font-size: 16px; /* Base font size 16px cho accessibility */
}

/* ========================================================================== */
/* PROMOTION BANNER - Banner khuyến mãi ở đầu trang                         */
/* ========================================================================== */

.promo-banner {
  /* Gradient background từ secondary sang primary color để thu hút attention */
  background: linear-gradient(
    135deg,
    /* Góc 135 độ tạo diagonal gradient đẹp mắt */ var(--secondary-color),
    /* Bắt đầu từ màu hồng */ var(--primary-color) /* Kết thúc ở màu xanh */
  );
  color: white; /* Text màu trắng để contrast với background */
  padding: var(--spacing-sm) var(--spacing-md); /* Padding vừa phải */
  text-align: center; /* Center text cho symmetry */
  position: relative; /* Relative để position close button */
  font-size: 0.9rem; /* Font nhỏ hơn một chút để không chiếm quá nhiều space */
  font-weight: 600; /* Font weight 600 để nổi bật */
  animation: slideDown 0.5s ease; /* Animation slide down khi load trang */
}

/* Nút đóng banner */
.promo-banner .close-btn {
  position: absolute; /* Absolute position để đặt ở góc phải */
  right: var(--spacing-md); /* Cách lề phải 1rem */
  top: 50%; /* Đặt ở giữa theo chiều dọc */
  transform: translateY(-50%); /* Center vertically với transform */
  background: none; /* Không có background */
  border: none; /* Không có border */
  color: white; /* Màu trắng để match với text */
  font-size: 1.2rem; /* Font size lớn hơn để dễ click */
  cursor: pointer; /* Pointer cursor để indicate clickable */
  opacity: 0.8; /* Opacity 0.8 để subtle hơn */
  transition: var(--transition); /* Smooth transition cho hover effect */
}

/* Hover effect cho close button */
.promo-banner .close-btn:hover {
  opacity: 1; /* Full opacity khi hover */
}

/* State khi banner bị ẩn */
.promo-banner.hidden {
  display: none; /* Hoàn toàn ẩn khỏi layout */
}

/* ========================================================================== */
/* HEADER STYLING - Header chính của trang với logo và navigation           */
/* ========================================================================== */

header {
  background-color: var(
    --bg-light
  ); /* Background trắng để clean và professional */
  color: var(--text-color); /* Text color từ CSS variables */
  padding: var(--spacing-lg) var(--spacing-xl); /* Padding lớn để tạo breathing room */
  text-align: center; /* Center align cho symmetry */
  box-shadow: var(--box-shadow); /* Subtle shadow để tách biệt với content */
  position: sticky; /* Sticky position để header luôn visible */
  top: 0; /* Stick ở top của viewport */
  z-index: 100; /* Z-index cao để luôn ở trên các elements khác */
}

/* Logo/Brand name styling */
header h1 {
  margin-bottom: var(--spacing-md); /* Margin bottom để tách với navigation */
  color: var(--primary-color); /* Primary color để highlight brand */
  font-weight: 700; /* Font weight đậm để nổi bật */
}

/* ========================================================================== */
/* NAVIGATION - Menu điều hướng chính                                        */
/* ========================================================================== */

nav {
  display: flex; /* Flexbox để layout horizontal */
  justify-content: center; /* Center navigation items */
  flex-wrap: wrap; /* Wrap trên mobile nếu cần */
  gap: var(--spacing-md); /* Gap giữa các nav items */
}

/* Navigation links styling */
nav a {
  color: var(--text-color); /* Default text color */
  text-decoration: none; /* Loại bỏ underline mặc định */
  padding: var(--spacing-xs) var(--spacing-md); /* Padding để tạo click area lớn hơn */
  font-weight: 600; /* Font weight 600 để nổi bật */
  border-radius: var(--border-radius); /* Bo góc để modern look */
  transition: var(--transition); /* Smooth transition cho hover effects */
}

/* Hover state cho navigation links */
nav a:hover {
  background-color: var(
    --primary-color
  ); /* Primary color background khi hover */
  color: white; /* Text trắng để contrast */
  text-decoration: none; /* Đảm bảo không có underline */
}

/* Main Content */
main {
  max-width: 1200px;
  margin: var(--spacing-xl) auto;
  padding: 0 var(--spacing-lg);
}

/* Banner/Slider */
.banner-slider {
  position: relative;
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  height: 400px;
}

.banner-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.banner-slide.active {
  opacity: 1;
}

.banner-content {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: var(--spacing-lg) var(--spacing-xl);
  max-width: 500px;
  margin-left: var(--spacing-xl);
  border-radius: var(--border-radius);
}

.banner-content h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.banner-content p {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
}

.banner-content .btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.banner-content .btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.slider-controls {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.slider-dot {
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
}

.slider-dot.active {
  background-color: white;
}

/* Store Introduction */
.store-intro {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.store-intro h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.8rem;
}

.store-intro p {
  color: var(--text-light);
  max-width: 800px;
  margin: 0 auto var(--spacing-md);
  line-height: 1.7;
}

.store-features {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 250px;
}

.feature i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.feature h3 {
  margin-bottom: var(--spacing-xs);
  font-size: 1.2rem;
  color: var(--text-color);
}

.feature p {
  text-align: center;
  font-size: 0.9rem;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: var(--spacing-sm);
}

.section-header h2 {
  color: var(--primary-color);
  font-size: 1.5rem;
  position: relative;
}

.section-header h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

.view-all {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: var(--transition);
}

.view-all:hover {
  color: var(--primary-dark);
}

.view-all i {
  margin-left: var(--spacing-xs);
}

/* Product Categories */
.product-categories {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  overflow-x: auto;
  padding-bottom: var(--spacing-sm);
  scrollbar-width: thin;
}

.product-categories::-webkit-scrollbar {
  height: 6px;
}

.product-categories::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 10px;
}

.category-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  border-radius: 30px;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.category-btn:hover,
.category-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Product Grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* Ensure grid works well with 5-6 products per row on large screens */
@media (min-width: 1400px) {
  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (min-width: 1600px) {
  .product-grid {
    grid-template-columns: repeat(6, 1fr);
    max-width: 1400px;
    margin: 0 auto var(--spacing-xl);
  }
}

/* Product Card */
.product {
  background-color: var(--bg-light);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.product:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 1;
}

.badge-new {
  background-color: var(--primary-color);
  color: white;
}

.badge-sale {
  background-color: var(--secondary-color);
  color: white;
}

.badge-hot {
  background-color: var(--accent-color);
  color: var(--text-color);
}

.product img {
  width: 100%;
  height: 220px;
  object-fit: cover;
  margin-bottom: var(--spacing-md);
  border-radius: calc(var(--border-radius) - 2px);
  transition: var(--transition);
  background-color: var(--bg-dark);
}

.product:hover img {
  transform: scale(1.05);
}

.product h3 {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.product h3 a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.product h3 a:hover {
  color: var(--primary-dark);
}

.product-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.current-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.old-price {
  font-size: 0.9rem;
  color: var(--text-light);
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: auto;
}

.product-actions button {
  flex: 1;
  background-color: var(--success-color);
  border: none;
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 0.95rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-actions .wishlist-btn {
  flex: 0 0 40px;
  background-color: var(--bg-dark);
  color: var(--text-color);
}

.product-actions .wishlist-btn:hover {
  background-color: var(--secondary-color);
  color: white;
}

.product-actions button:hover {
  background-color: var(--success-dark);
}

/* Shopping Cart Table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--spacing-xl);
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

table,
th,
td {
  border: 1px solid var(--border-color);
}

th,
td {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
}

th {
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

td button {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

td button:hover {
  opacity: 0.9;
}

h3 {
  margin-top: var(--spacing-xl);
  text-align: right;
  font-size: 1.4rem;
  color: var(--text-color);
}

/* Checkout Form */
form {
  max-width: 500px;
  margin: var(--spacing-xl) auto;
  background-color: var(--bg-light);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

form input {
  width: 100%;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

form input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(58, 134, 255, 0.2);
}

form button {
  width: 100%;
  background-color: var(--primary-color);
  border: none;
  padding: var(--spacing-md);
  color: white;
  font-size: 1.1rem;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 600;
}

form button:hover {
  background-color: var(--primary-dark);
}

/* Footer Styling */
footer {
  background-color: var(--bg-light);
  color: var(--text-color);
  padding: var(--spacing-xl) var(--spacing-lg);
  margin-top: var(--spacing-xl);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-section h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.2rem;
  text-align: left;
}

.footer-section p,
.footer-section a {
  color: var(--text-light);
  margin-bottom: var(--spacing-sm);
  font-size: 0.95rem;
}

.footer-section a {
  display: block;
  text-decoration: none;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  header {
    padding: var(--spacing-md);
  }

  nav {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .banner-slider {
    height: 300px;
  }

  .banner-content {
    max-width: 80%;
    margin-left: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .banner-content h2 {
    font-size: 1.5rem;
  }

  .banner-content p {
    font-size: 1rem;
  }

  .store-intro {
    padding: var(--spacing-lg);
  }

  .store-features {
    gap: var(--spacing-lg);
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-md);
  }

  .product img {
    height: 180px;
  }

  form {
    padding: var(--spacing-lg);
  }

  table,
  th,
  td {
    font-size: 0.9rem;
  }

  th,
  td {
    padding: var(--spacing-sm);
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  body {
    font-size: 14px;
  }

  header {
    padding: var(--spacing-sm);
  }

  header h1 {
    font-size: 1.5rem;
  }

  nav {
    gap: var(--spacing-xs);
  }

  nav a {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.9rem;
  }

  .banner-slider {
    height: 200px;
    margin-bottom: var(--spacing-md);
  }

  .banner-content {
    max-width: 90%;
    margin-left: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .banner-content h2 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
  }

  .banner-content p {
    font-size: 0.85rem;
    margin-bottom: var(--spacing-xs);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .banner-content .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.85rem;
  }

  .slider-controls {
    bottom: var(--spacing-sm);
  }

  .slider-dot {
    width: 8px;
    height: 8px;
  }

  .store-intro {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .store-intro h2 {
    font-size: 1.4rem;
  }

  .store-intro p {
    font-size: 0.9rem;
  }

  .store-features {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
  }

  .feature {
    max-width: 100%;
  }

  .section-header h2 {
    font-size: 1.2rem;
  }

  .product-categories {
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
  }

  .category-btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.85rem;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .product {
    padding: var(--spacing-md);
  }

  .product img {
    height: 180px;
  }

  .product h3 {
    font-size: 1.1rem;
  }

  .product-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .product-actions .wishlist-btn {
    flex: 1;
  }

  main {
    padding: 0 var(--spacing-md);
    margin: var(--spacing-md) auto;
  }

  form {
    padding: var(--spacing-md);
  }

  table {
    display: block;
    overflow-x: auto;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .footer-section {
    margin-bottom: var(--spacing-md);
  }

  .footer-section a {
    display: inline-block;
    margin: 0 var(--spacing-xs) var(--spacing-xs) 0;
  }
}

/* Marketing Features */

/* Pop-up Modal */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.popup-overlay.show {
  opacity: 1;
  visibility: visible;
}

.popup-content {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  max-width: 500px;
  width: 90%;
  position: relative;
  text-align: center;
  transform: scale(0.7);
  transition: transform 0.3s ease;
}

.popup-overlay.show .popup-content {
  transform: scale(1);
}

.popup-close {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-light);
  transition: var(--transition);
}

.popup-close:hover {
  color: var(--text-color);
}

.popup-title {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
}

.popup-discount {
  background: var(--secondary-color);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin: var(--spacing-md) 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.popup-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.popup-actions button {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.popup-actions .btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.popup-actions .btn-secondary {
  background-color: var(--bg-dark);
  color: var(--text-color);
}

.popup-actions button:hover {
  transform: translateY(-2px);
}

/* Countdown Timer */
.countdown-section {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: var(--spacing-xl);
  text-align: center;
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.countdown-title {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.countdown-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  min-width: 80px;
}

.countdown-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.countdown-label {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: var(--spacing-xs);
}

/* Testimonials */
.testimonials-section {
  background-color: var(--bg-light);
  padding: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.testimonials-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.testimonial {
  text-align: center;
  padding: var(--spacing-lg);
  display: none;
}

.testimonial.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.testimonial-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto var(--spacing-md);
  object-fit: cover;
}

.testimonial-rating {
  color: #ffd700;
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
}

.testimonial-text {
  font-style: italic;
  font-size: 1.1rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-color);
}

.testimonial-author {
  font-weight: 600;
  color: var(--primary-color);
}

.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--border-color);
  cursor: pointer;
  transition: var(--transition);
}

.testimonial-dot.active {
  background-color: var(--primary-color);
}

/* Newsletter */
.newsletter-section {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: var(--spacing-xl);
  text-align: center;
  margin: var(--spacing-xl) 0;
  border-radius: var(--border-radius-lg);
}

.newsletter-title {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
}

.newsletter-subtitle {
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.newsletter-form {
  display: flex;
  max-width: 400px;
  margin: 0 auto;
  gap: var(--spacing-sm);
}

.newsletter-input {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.newsletter-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 600;
  transition: var(--transition);
}

.newsletter-btn:hover {
  background-color: #e55039;
  transform: translateY(-2px);
}

/* Social Proof */
.social-proof {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  max-width: 300px;
  z-index: 500;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.social-proof.show {
  transform: translateX(0);
}

.social-proof-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.social-proof-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.social-proof-text {
  flex: 1;
  font-size: 0.9rem;
}

.social-proof-name {
  font-weight: 600;
  color: var(--primary-color);
}

.social-proof-action {
  color: var(--text-light);
}

/* ========================================================================== */
/* ENHANCED ANIMATIONS - Keyframes cho các hiệu ứng animation               */
/* ========================================================================== */

/* Animation slide down từ trên xuống - dùng cho promo banner */
@keyframes slideDown {
  from {
    transform: translateY(-100%); /* Bắt đầu từ vị trí -100% (ẩn phía trên) */
    opacity: 0; /* Trong suốt hoàn toàn */
  }
  to {
    transform: translateY(0); /* Kết thúc ở vị trí bình thường */
    opacity: 1; /* Hiện rõ hoàn toàn */
  }
}

/* Animation fade in với slight movement - dùng cho content loading */
@keyframes fadeIn {
  from {
    opacity: 0; /* Bắt đầu trong suốt */
    transform: translateY(20px); /* Bắt đầu từ vị trí thấp hơn 20px */
  }
  to {
    opacity: 1; /* Kết thúc hiện rõ */
    transform: translateY(0); /* Kết thúc ở vị trí bình thường */
  }
}

/* Animation pulse - dùng cho games button để thu hút attention */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1); /* Kích thước bình thường ở đầu và cuối */
  }
  50% {
    transform: scale(1.05); /* Phóng to 5% ở giữa animation */
  }
}

/* Animation bounce phức tạp - dùng cho achievement notifications */
@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    /* Sử dụng cubic-bezier cho natural bounce effect */
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0); /* Vị trí bình thường */
  }
  40%,
  43% {
    /* Timing function khác cho bounce up */
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -30px, 0); /* Bounce lên cao nhất */
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -15px, 0); /* Bounce thấp hơn */
  }
  90% {
    transform: translate3d(0, -4px, 0); /* Bounce cuối cùng rất nhẹ */
  }
}

/* Animation shake - dùng cho wrong matches trong memory game */
@keyframes shake {
  0%,
  100% {
    transform: translateX(0); /* Vị trí bình thường ở đầu và cuối */
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px); /* Di chuyển sang trái */
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px); /* Di chuyển sang phải */
  }
}

/* Animation glow - tạo hiệu ứng phát sáng cho elements đặc biệt */
@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px var(--primary-color); /* Glow nhẹ ở đầu/cuối */
  }
  50% {
    /* Glow mạnh ở giữa với multiple shadows */
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
}

/* Animation sparkle - hiệu ứng lấp lánh cho decorative elements */
@keyframes sparkle {
  0%,
  100% {
    opacity: 0; /* Ẩn ở đầu và cuối */
    transform: scale(0); /* Scale về 0 */
  }
  50% {
    opacity: 1; /* Hiện rõ ở giữa */
    transform: scale(1); /* Scale bình thường */
  }
}

/* Animation wheel spin - quay bánh xe may mắn */
@keyframes wheelSpin {
  0% {
    transform: rotate(0deg); /* Bắt đầu từ 0 độ */
  }
  100% {
    transform: rotate(1800deg); /* Quay 5 vòng (1800 độ) */
  }
}

/* Animation card flip - lật thẻ trong memory game */
@keyframes cardFlip {
  0% {
    transform: rotateY(0deg); /* Mặt trước (0 độ) */
  }
  50% {
    transform: rotateY(-90deg); /* Giữa quá trình lật (90 độ) */
  }
  100% {
    transform: rotateY(-180deg); /* Mặt sau (180 độ) */
  }
}

/* Animation confetti - hiệu ứng pháo giấy khi thắng */
@keyframes confetti {
  0% {
    transform: translateY(0) rotate(0deg); /* Bắt đầu ở vị trí ban đầu */
    opacity: 1; /* Hiện rõ */
  }
  100% {
    transform: translateY(-100vh) rotate(720deg); /* Bay lên trên và quay 2 vòng */
    opacity: 0; /* Mờ dần */
  }
}

/* ========================================================================== */
/* MINI GAMES STYLES - Styling cho hệ thống mini games                      */
/* ========================================================================== */

/* Floating Game Button - Nút games nổi bên phải màn hình */
.games-button {
  position: fixed; /* Fixed position để luôn hiện trên màn hình */
  top: 50%; /* Đặt ở giữa màn hình theo chiều dọc */
  right: 20px; /* Cách lề phải 20px */
  transform: translateY(-50%); /* Center vertically với transform */
  /* Gradient background để thu hút attention */
  background: linear-gradient(
    135deg,
    /* Góc 135 độ cho diagonal gradient */ var(--primary-color),
    /* Từ primary color */ var(--secondary-color) /* Đến secondary color */
  );
  color: white; /* Text màu trắng để contrast */
  border: none; /* Không có border */
  border-radius: 50px; /* Bo góc tròn hoàn toàn */
  padding: var(--spacing-md) var(--spacing-lg); /* Padding để button có size phù hợp */
  font-weight: 600; /* Font weight đậm để nổi bật */
  cursor: pointer; /* Pointer cursor để indicate clickable */
  box-shadow: var(--box-shadow); /* Đổ bóng để tạo depth */
  z-index: 999; /* Z-index cao để luôn ở trên */
  transition: var(--transition); /* Smooth transition cho hover effects */
  animation: pulse 2s infinite; /* Animation pulse liên tục để thu hút attention */
}

/* Hover effect cho games button */
.games-button:hover {
  /* Kết hợp transform để vừa center vừa scale */
  transform: translateY(-50%) scale(1.05);
  /* Enhanced shadow khi hover */
  box-shadow: 0 8px 25px rgba(58, 134, 255, 0.3);
}

/* Icon trong games button */
.games-button i {
  margin-right: var(--spacing-xs); /* Khoảng cách giữa icon và text */
}

/* Game Modal - Modal popup chứa các mini games */
.game-modal {
  position: fixed; /* Fixed position để overlay toàn màn hình */
  top: 0; /* Bắt đầu từ top */
  left: 0; /* Bắt đầu từ left */
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  background-color: rgba(0, 0, 0, 0.8); /* Semi-transparent black backdrop */
  display: flex; /* Flexbox để center content */
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
  z-index: 1001; /* Z-index cao hơn games button */
  opacity: 0; /* Ẩn ban đầu */
  visibility: hidden; /* Ẩn khỏi screen readers khi không active */
  transition: all 0.3s ease; /* Smooth transition cho show/hide */
}

/* State khi modal được hiển thị */
.game-modal.show {
  opacity: 1; /* Hiện rõ */
  visibility: visible; /* Hiện cho screen readers */
}

/* Content container của modal */
.game-modal-content {
  background: white; /* Background trắng */
  border-radius: var(--border-radius-lg); /* Bo góc lớn */
  padding: var(--spacing-xl); /* Padding lớn cho breathing room */
  max-width: 600px; /* Max width để không quá rộng trên desktop */
  width: 90%; /* 90% width trên mobile */
  max-height: 80vh; /* Max height 80% viewport để có scroll */
  overflow-y: auto; /* Scroll nếu content quá dài */
  position: relative; /* Relative để position close button */
  transform: scale(0.7); /* Scale nhỏ ban đầu cho animation */
  transition: transform 0.3s ease; /* Smooth scale transition */
}

/* Scale animation khi modal show */
.game-modal.show .game-modal-content {
  transform: scale(1); /* Scale về bình thường khi show */
}

/* Close button của modal */
.game-close {
  position: absolute; /* Absolute position trong modal content */
  top: var(--spacing-md); /* Cách top 1rem */
  right: var(--spacing-md); /* Cách right 1rem */
  background: none; /* Không có background */
  border: none; /* Không có border */
  font-size: 1.5rem; /* Font size lớn để dễ click */
  cursor: pointer; /* Pointer cursor */
  color: var(--text-light); /* Màu xám nhạt */
  transition: var(--transition); /* Smooth transition cho hover */
}

/* Hover effect cho close button */
.game-close:hover {
  color: var(--text-color); /* Đậm hơn khi hover */
}

/* Game Selection Grid - Grid layout cho việc chọn game */
.games-grid {
  display: grid; /* CSS Grid layout */
  /* Auto-fit với minmax để responsive - tối thiểu 200px, tối đa 1fr */
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg); /* Gap lớn giữa các cards */
  margin-top: var(--spacing-lg); /* Margin top để tách với header */
}

/* Individual Game Card */
.game-card {
  background: var(--bg-light); /* Background sáng */
  border: 2px solid var(--border-color); /* Border nhẹ */
  border-radius: var(--border-radius); /* Bo góc */
  padding: var(--spacing-lg); /* Padding lớn cho breathing room */
  text-align: center; /* Center align content */
  cursor: pointer; /* Pointer cursor để indicate clickable */
  transition: var(--transition); /* Smooth transition cho hover effects */
  position: relative; /* Relative để position pseudo-element */
  overflow: hidden; /* Ẩn overflow cho shine effect */
}

/* Hover effects cho game card */
.game-card:hover {
  border-color: var(--primary-color); /* Primary color border khi hover */
  transform: translateY(-5px); /* Lift effect khi hover */
  box-shadow: var(--box-shadow); /* Đổ bóng khi hover */
}

/* Shine effect với pseudo-element */
.game-card::before {
  content: ""; /* Empty content cho pseudo-element */
  position: absolute; /* Absolute position */
  top: 0; /* Full height */
  left: -100%; /* Bắt đầu từ ngoài bên trái */
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  /* Gradient shine effect */
  background: linear-gradient(
    90deg,
    /* Horizontal gradient */ transparent,
    /* Trong suốt ở đầu */ rgba(255, 255, 255, 0.2),
    /* Trắng semi-transparent ở giữa */ transparent /* Trong suốt ở cuối */
  );
  transition: 0.5s; /* Slow transition cho smooth effect */
}

/* Shine animation khi hover */
.game-card:hover::before {
  left: 100%; /* Di chuyển sang phải khi hover */
}

.game-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.game-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.game-description {
  font-size: 0.9rem;
  color: var(--text-light);
  margin-bottom: var(--spacing-md);
}

.game-status {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.game-status.available {
  background-color: var(--success-color);
  color: white;
}

.game-status.played {
  background-color: var(--text-light);
  color: white;
}

.game-status.locked {
  background-color: var(--border-color);
  color: var(--text-light);
}

/* ========================================================================== */
/* LUCKY WHEEL GAME - Vòng quay may mắn                                     */
/* ========================================================================== */

/* Container chính của lucky wheel game */
.lucky-wheel-container {
  text-align: center; /* Center align tất cả content */
  padding: var(--spacing-lg); /* Padding lớn cho breathing room */
}

/* Container chứa wheel và pointer */
.wheel-container {
  position: relative; /* Relative để position pointer */
  width: 300px; /* Fixed width cho desktop */
  height: 300px; /* Square aspect ratio */
  margin: 0 auto var(--spacing-lg); /* Center horizontally với margin bottom */
}

/* Bánh xe chính */
.wheel {
  width: 100%; /* Full width của container */
  height: 100%; /* Full height của container */
  border-radius: 50%; /* Tròn hoàn toàn */
  border: 8px solid var(--primary-color); /* Border dày với primary color */
  position: relative; /* Relative để position segments */
  overflow: hidden; /* Ẩn overflow của segments */
  /* Custom cubic-bezier cho realistic spin effect */
  transition: transform 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* Glow effect xung quanh wheel */
  box-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
}

/* Class khi wheel đang spinning */
.wheel.spinning {
  /* Sử dụng animation thay vì transition cho better control */
  animation: wheelSpin 4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Từng segment của wheel */
.wheel-segment {
  position: absolute; /* Absolute để position chính xác */
  width: 50%; /* 50% width để tạo triangle shape */
  height: 50%; /* 50% height để tạo triangle shape */
  transform-origin: 100% 100%; /* Transform từ góc bottom-right */
  display: flex; /* Flexbox để center text */
  align-items: center; /* Center vertically */
  justify-content: center; /* Center horizontally */
  font-weight: 600; /* Font weight đậm để dễ đọc */
  color: white; /* Text màu trắng để contrast */
  font-size: 0.9rem; /* Font size vừa phải */
}

/* Pointer chỉ vị trí trúng thưởng */
.wheel-pointer {
  position: absolute; /* Absolute position trong wheel container */
  top: -10px; /* Đặt phía trên wheel một chút */
  left: 50%; /* Center horizontally */
  transform: translateX(-50%); /* Perfect center với transform */
  width: 0; /* Width 0 cho triangle */
  height: 0; /* Height 0 cho triangle */
  /* Tạo triangle shape bằng borders */
  border-left: 15px solid transparent; /* Left border trong suốt */
  border-right: 15px solid transparent; /* Right border trong suốt */
  border-top: 30px solid var(--secondary-color); /* Top border tạo triangle */
  z-index: 10; /* Z-index cao để luôn ở trên wheel */
}

.spin-button {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  border: none;
  border-radius: 50px;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.spin-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(58, 134, 255, 0.3);
}

.spin-button:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Scratch Card */
.scratch-card-container {
  text-align: center;
  padding: var(--spacing-lg);
}

.scratch-card {
  position: relative;
  width: 300px;
  height: 200px;
  margin: 0 auto var(--spacing-lg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.scratch-card-back {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--success-color),
    var(--accent-color)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
}

.scratch-card-front {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #c0c0c0, #808080);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  cursor: crosshair;
}

/* Daily Check-in */
.checkin-container {
  padding: var(--spacing-lg);
}

.checkin-calendar {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.checkin-day {
  aspect-ratio: 1;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  background: var(--bg-light);
}

.checkin-day.checked {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.checkin-day.today {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  animation: pulse 1s infinite;
}

.checkin-day.future {
  opacity: 0.5;
  cursor: not-allowed;
}

.day-number {
  font-weight: 600;
  font-size: 1.1rem;
}

.day-reward {
  font-size: 0.7rem;
  margin-top: 2px;
}

.checkin-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-dark);
  border-radius: var(--border-radius);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Memory Game */
.memory-game-container {
  padding: var(--spacing-lg);
}

.memory-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-sm);
  max-width: 400px;
  margin: 0 auto var(--spacing-lg);
}

.memory-card {
  aspect-ratio: 1;
  border-radius: var(--border-radius);
  cursor: pointer;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.memory-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.memory-card.flipped {
  transform: rotateY(180deg);
}

.memory-card.matched {
  opacity: 0.6;
  transform: rotateY(180deg) scale(0.9);
}

.memory-card-face {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
}

.memory-card-front {
  background: var(--primary-color);
  color: white;
}

.memory-card-back {
  background: var(--bg-light);
  border: 2px solid var(--border-color);
  transform: rotateY(180deg);
}

.memory-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-dark);
  border-radius: var(--border-radius);
}

.memory-stat {
  text-align: center;
}

.memory-stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.memory-stat-label {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* ========================================================================== */
/* RESPONSIVE DESIGN - Media queries cho mobile và tablet                   */
/* ========================================================================== */

/* Mobile-first approach: Breakpoint 768px cho tablet và mobile */
@media (max-width: 768px) {
  /* Marketing features responsive adjustments */
  .countdown-timer {
    gap: var(--spacing-md); /* Giảm gap giữa countdown items */
  }

  .countdown-item {
    min-width: 60px; /* Giảm min-width cho mobile */
    padding: var(--spacing-sm); /* Giảm padding */
  }

  .countdown-number {
    font-size: 1.5rem; /* Giảm font size cho mobile */
  }

  .newsletter-form {
    flex-direction: column; /* Stack vertically trên mobile */
  }

  .popup-content {
    padding: var(--spacing-lg); /* Giảm padding cho mobile */
  }

  .popup-actions {
    flex-direction: column; /* Stack buttons vertically */
  }

  .social-proof {
    left: 10px; /* Giảm margin cho mobile */
    right: 10px; /* Giảm margin cho mobile */
    max-width: none; /* Không giới hạn max-width */
  }

  /* ===== GAMES RESPONSIVE ADJUSTMENTS ===== */

  /* Games button adjustments cho mobile */
  .games-button {
    right: 10px; /* Gần lề hơn trên mobile */
    padding: var(--spacing-sm) var(--spacing-md); /* Padding nhỏ hơn */
    font-size: 0.9rem; /* Font size nhỏ hơn */
  }

  /* Games grid chuyển thành single column */
  .games-grid {
    grid-template-columns: 1fr; /* Single column layout trên mobile */
  }

  /* Lucky wheel size adjustment */
  .wheel-container {
    width: 250px; /* Nhỏ hơn cho mobile */
    height: 250px; /* Maintain aspect ratio */
  }

  /* Scratch card size adjustment */
  .scratch-card {
    width: 250px; /* Nhỏ hơn cho mobile */
    height: 150px; /* Maintain aspect ratio */
  }

  /* Memory game grid adjustment */
  .memory-grid {
    max-width: 300px; /* Nhỏ hơn cho mobile */
  }

  /* Check-in calendar spacing */
  .checkin-calendar {
    gap: var(--spacing-xs); /* Gap nhỏ hơn cho mobile */
  }
}

/* ========================================================================== */
/* LOADING STATES & PROGRESS INDICATORS - UI feedback cho user              */
/* ========================================================================== */

/* Loading spinner - hiển thị khi đang load */
.loading-spinner {
  display: inline-block; /* Inline để có thể đặt trong text */
  width: 20px; /* Size nhỏ gọn */
  height: 20px; /* Square aspect ratio */
  border: 3px solid rgba(255, 255, 255, 0.3); /* Border nhạt */
  border-radius: 50%; /* Tròn hoàn toàn */
  border-top-color: white; /* Top border màu trắng để tạo spinning effect */
  animation: spin 1s ease-in-out infinite; /* Animation quay liên tục */
}

/* Animation quay cho spinner */
@keyframes spin {
  to {
    transform: rotate(360deg); /* Quay 360 độ */
  }
}

/* Progress bar container */
.progress-bar {
  width: 100%; /* Full width */
  height: 8px; /* Height mỏng */
  background-color: var(--bg-dark); /* Background xám */
  border-radius: 4px; /* Bo góc nhẹ */
  overflow: hidden; /* Ẩn overflow của fill */
  margin: var(--spacing-sm) 0; /* Margin top/bottom */
}

/* Progress fill - phần đã hoàn thành */
.progress-fill {
  height: 100%; /* Full height của container */
  /* Gradient background để đẹp mắt */
  background: linear-gradient(
    90deg,
    /* Horizontal gradient */ var(--primary-color),
    /* Từ primary color */ var(--secondary-color) /* Đến secondary color */
  );
  border-radius: 4px; /* Bo góc giống container */
  transition: width 0.3s ease; /* Smooth transition khi width thay đổi */
  position: relative; /* Relative để position pseudo-element */
}

/* Animated stripes effect trên progress bar */
.progress-fill::after {
  content: ""; /* Empty content cho pseudo-element */
  position: absolute; /* Absolute để cover toàn bộ fill */
  top: 0; /* Full coverage */
  left: 0;
  bottom: 0;
  right: 0;
  /* Diagonal stripes pattern */
  background-image: linear-gradient(
    -45deg,
    /* Diagonal -45 độ */ rgba(255, 255, 255, 0.2) 25%,
    /* Stripe sáng */ transparent 25%,
    /* Trong suốt */ transparent 50%,
    /* Trong suốt */ rgba(255, 255, 255, 0.2) 50%,
    /* Stripe sáng */ rgba(255, 255, 255, 0.2) 75%,
    /* Stripe sáng */ transparent 75%,
    /* Trong suốt */ transparent /* Trong suốt */
  );
  background-size: 50px 50px; /* Size của pattern */
  animation: move 2s linear infinite; /* Animation di chuyển stripes */
}

/* Animation di chuyển stripes */
@keyframes move {
  0% {
    background-position: 0 0; /* Vị trí ban đầu */
  }
  100% {
    background-position: 50px 50px; /* Di chuyển 1 pattern unit */
  }
}

/* Achievement System */
.achievement-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #8b5a00;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  animation: bounce 0.6s ease;
}

.achievement-notification {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  text-align: center;
  z-index: 1002;
  animation: achievementPop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes achievementPop {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.3);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Confetti Effect */
.confetti-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1001;
}

.confetti-piece {
  position: absolute;
  width: 10px;
  height: 10px;
  background: var(--primary-color);
  animation: confetti 3s linear infinite;
}

.confetti-piece:nth-child(odd) {
  background: var(--secondary-color);
  border-radius: 50%;
}

.confetti-piece:nth-child(3n) {
  background: var(--accent-color);
  width: 6px;
  height: 6px;
}

/* Tutorial Overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1003;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tutorial-content {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  max-width: 500px;
  width: 90%;
  text-align: center;
  position: relative;
}

.tutorial-step {
  display: none;
}

.tutorial-step.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

.tutorial-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-lg);
}

/* Sound Control */
.sound-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: var(--transition);
  box-shadow: var(--box-shadow);
}

.sound-toggle:hover {
  background: white;
  transform: scale(1.1);
}

.sound-toggle.muted {
  opacity: 0.5;
}

/* Difficulty Selector */
.difficulty-selector {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  justify-content: center;
}

.difficulty-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  background: var(--bg-light);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
}

.difficulty-btn.active {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
}

.difficulty-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Combo System */
.combo-display {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--accent-color)
  );
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-weight: 600;
  transform: scale(0);
  transition: transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.combo-display.active {
  transform: scale(1);
  animation: pulse 0.6s ease;
}

/* ========================================================================== */
/* ACCESSIBILITY IMPROVEMENTS - Cải thiện khả năng tiếp cận                */
/* ========================================================================== */

/* Screen reader only class - ẩn visual nhưng vẫn đọc được bởi screen reader */
.sr-only {
  position: absolute; /* Absolute để loại khỏi normal flow */
  width: 1px; /* Width tối thiểu */
  height: 1px; /* Height tối thiểu */
  padding: 0; /* Không có padding */
  margin: -1px; /* Negative margin để ẩn hoàn toàn */
  overflow: hidden; /* Ẩn overflow */
  clip: rect(0, 0, 0, 0); /* Clip để ẩn visual */
  white-space: nowrap; /* Không wrap text */
  border: 0; /* Không có border */
}

/* Focus visible styling cho keyboard navigation */
.focus-visible {
  outline: 3px solid var(--primary-color); /* Outline rõ ràng với primary color */
  outline-offset: 2px; /* Offset để không bị overlap */
}

/* ========================================================================== */
/* HIGH CONTRAST MODE - Hỗ trợ cho người dùng cần high contrast             */
/* ========================================================================== */

/* Media query cho users prefer high contrast */
@media (prefers-contrast: high) {
  /* Game cards với border đậm hơn */
  .game-card {
    border: 3px solid var(--text-color); /* Border dày hơn với text color */
  }

  /* Wheel segments với border để phân biệt */
  .wheel-segment {
    border: 2px solid var(--text-color); /* Border để separate segments */
  }

  /* Memory card faces với border rõ ràng */
  .memory-card-face {
    border: 2px solid var(--text-color); /* Border để distinguish cards */
  }
}

/* ========================================================================== */
/* REDUCED MOTION - Tôn trọng preference của user về motion                 */
/* ========================================================================== */

/* Media query cho users prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  /* Disable tất cả animations và transitions */
  * {
    animation-duration: 0.01ms !important; /* Animation cực ngắn */
    animation-iteration-count: 1 !important; /* Chỉ chạy 1 lần */
    transition-duration: 0.01ms !important; /* Transition cực ngắn */
  }

  /* Specific elements cần disable hoàn toàn */
  .wheel {
    transition: none; /* Không có transition cho wheel */
  }

  .memory-card {
    transition: none; /* Không có transition cho memory cards */
  }
}
