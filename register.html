<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON>ý - Cửa <PERSON> Online</title>
    <link rel="stylesheet" href="style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      /* Bố cục chính */
      .auth-page {
        min-height: calc(100vh - 200px);
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--bg-color);
        padding: var(--spacing-xl) var(--spacing-md);
        position: relative;
        overflow: hidden;
      }

      /* Background decoration */
      .auth-page::before {
        content: "";
        position: absolute;
        top: -10%;
        right: -10%;
        width: 500px;
        height: 500px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--accent-color)
        );
        opacity: 0.1;
        z-index: 0;
      }

      .auth-page::after {
        content: "";
        position: absolute;
        bottom: -10%;
        left: -10%;
        width: 400px;
        height: 400px;
        border-radius: 50%;
        background: linear-gradient(
          135deg,
          var(--secondary-color),
          var(--primary-color)
        );
        opacity: 0.1;
        z-index: 0;
      }

      /* Container chính */
      .auth-container {
        display: flex;
        max-width: 900px;
        width: 100%;
        background-color: var(--bg-light);
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        position: relative;
        z-index: 1;
      }

      /* Phần hình ảnh minh họa */
      .auth-image {
        flex: 1;
        background-image: url("https://images.unsplash.com/photo-1607082349566-187342175e2f?q=80&w=1000&auto=format&fit=crop");
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .auth-image::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(58, 134, 255, 0.8),
          rgba(255, 0, 110, 0.8)
        );
        opacity: 0.7;
      }

      .auth-image-content {
        position: relative;
        color: white;
        text-align: center;
        padding: var(--spacing-xl);
        max-width: 80%;
      }

      .auth-image-content h2 {
        font-size: 2rem;
        margin-bottom: var(--spacing-md);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .auth-image-content p {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: var(--spacing-lg);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }

      /* Phần form */
      .auth-form {
        flex: 1;
        padding: var(--spacing-xl);
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        max-height: 700px;
      }

      .auth-header {
        text-align: center;
        margin-bottom: var(--spacing-lg);
      }

      .auth-header h2 {
        color: var(--primary-color);
        margin-bottom: var(--spacing-sm);
        font-size: 1.8rem;
      }

      .auth-header p {
        color: var(--text-light);
      }

      /* Form styling */
      .form-group {
        margin-bottom: var(--spacing-md);
        position: relative;
      }

      .form-group label {
        display: block;
        margin-bottom: var(--spacing-xs);
        font-weight: 600;
        color: var(--text-color);
        transition: var(--transition);
      }

      .form-group input {
        width: 100%;
        padding: var(--spacing-md);
        padding-left: 40px;
        border: 2px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: var(--bg-light);
      }

      .form-group i {
        position: absolute;
        left: 15px;
        top: 38px;
        color: var(--text-light);
        transition: var(--transition);
      }

      .form-group input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(58, 134, 255, 0.2);
      }

      .form-group input:focus + i {
        color: var(--primary-color);
      }

      .form-group .error {
        color: var(--secondary-color);
        font-size: 0.9rem;
        margin-top: var(--spacing-xs);
        display: none;
      }

      .form-group.error input {
        border-color: var(--secondary-color);
      }

      .form-group.error .error {
        display: block;
      }

      /* Password toggle */
      .password-toggle {
        position: relative;
      }

      .password-toggle input {
        padding-right: 40px;
      }

      .password-toggle .toggle-btn {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-light);
        cursor: pointer;
        transition: var(--transition);
      }

      .password-toggle .toggle-btn:hover {
        color: var(--primary-color);
      }

      /* Form actions */
      .form-actions {
        margin-top: var(--spacing-lg);
      }

      .form-actions button {
        width: 100%;
        background: linear-gradient(
          to right,
          var(--primary-color),
          var(--primary-dark)
        );
        color: white;
        border: none;
        padding: var(--spacing-md);
        border-radius: var(--border-radius);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .form-actions button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: 0.5s;
      }

      .form-actions button:hover::before {
        left: 100%;
      }

      .form-actions button:hover {
        box-shadow: 0 5px 15px rgba(58, 134, 255, 0.3);
        transform: translateY(-2px);
      }

      /* Footer */
      .auth-footer {
        text-align: center;
        margin-top: var(--spacing-lg);
        color: var(--text-light);
      }

      .auth-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: var(--transition);
      }

      .auth-footer a:hover {
        color: var(--primary-dark);
        text-decoration: underline;
      }

      /* Responsive */
      @media (max-width: 768px) {
        .auth-page {
          padding: var(--spacing-md);
        }

        .auth-container {
          flex-direction: column;
          max-width: 500px;
        }

        .auth-image {
          display: none;
        }

        .auth-form {
          padding: var(--spacing-lg);
          max-height: none;
        }

        .form-group input {
          font-size: 16px; /* Prevents zoom on mobile */
        }
      }

      @media (max-width: 480px) {
        .auth-form {
          padding: var(--spacing-md);
        }

        .auth-header h2 {
          font-size: 1.5rem;
        }

        .progress-container {
          margin-bottom: var(--spacing-md);
        }
      }

      /* Animation */
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .auth-header,
      .form-group,
      .form-actions,
      .auth-footer {
        animation: fadeIn 0.5s ease forwards;
      }

      .form-group:nth-child(1) {
        animation-delay: 0.1s;
      }
      .form-group:nth-child(2) {
        animation-delay: 0.2s;
      }
      .form-group:nth-child(3) {
        animation-delay: 0.3s;
      }
      .form-group:nth-child(4) {
        animation-delay: 0.4s;
      }
      .form-group:nth-child(5) {
        animation-delay: 0.5s;
      }
      .form-actions {
        animation-delay: 0.6s;
      }
      .auth-footer {
        animation-delay: 0.7s;
      }

      /* Progress bar */
      .progress-container {
        width: 100%;
        height: 5px;
        background-color: var(--border-color);
        border-radius: 10px;
        margin-bottom: var(--spacing-lg);
        overflow: hidden;
      }

      .progress-bar {
        height: 100%;
        background: linear-gradient(
          to right,
          var(--primary-color),
          var(--accent-color)
        );
        width: 0;
        transition: width 0.3s ease;
      }
    </style>
  </head>
  <body>
    <header>
      <h1>Cửa Hàng Online</h1>
      <nav>
        <a href="index.html">Trang Chủ</a>
        <a href="product.html">Sản Phẩm</a>
        <a href="cart.html">Giỏ Hàng</a>
        <a href="login.html">Đăng Nhập</a>
        <a href="register.html">Đăng Ký</a>
      </nav>
    </header>

    <main>
      <div class="auth-page">
        <div class="auth-container">
          <div class="auth-image">
            <div class="auth-image-content">
              <h2>Tham gia cùng chúng tôi</h2>
              <p>
                Đăng ký tài khoản để nhận nhiều ưu đãi đặc biệt và cập nhật
                những bộ sưu tập mới nhất.
              </p>
            </div>
          </div>

          <div class="auth-form">
            <div class="auth-header">
              <h2>Đăng Ký Tài Khoản</h2>
              <p>Vui lòng điền đầy đủ thông tin để tạo tài khoản mới</p>
            </div>

            <div class="progress-container">
              <div class="progress-bar" id="passwordStrength"></div>
            </div>

            <form id="registerForm" novalidate>
              <div class="form-group">
                <label for="fullname">Họ tên</label>
                <input type="text" id="fullname" name="fullname" required />
                <i class="fas fa-user"></i>
                <div class="error">
                  Vui lòng nhập họ tên đầy đủ của bạn (ít nhất 3 ký tự)
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required />
                <i class="fas fa-envelope"></i>
                <div class="error">Vui lòng nhập email hợp lệ</div>
              </div>

              <div class="form-group">
                <label for="phone">Số điện thoại</label>
                <input type="tel" id="phone" name="phone" required />
                <i class="fas fa-phone"></i>
                <div class="error">
                  Vui lòng nhập số điện thoại hợp lệ (10-11 số)
                </div>
              </div>

              <div class="form-group">
                <label for="password">Mật khẩu</label>
                <div class="password-toggle">
                  <input
                    type="password"
                    id="password"
                    name="password"
                    required
                  />
                  <i class="fas fa-lock"></i>
                  <button
                    type="button"
                    class="toggle-btn"
                    onclick="togglePassword('password')"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div class="error">
                  Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường
                  và số
                </div>
              </div>

              <div class="form-group">
                <label for="confirmPassword">Xác nhận mật khẩu</label>
                <div class="password-toggle">
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    required
                  />
                  <i class="fas fa-lock"></i>
                  <button
                    type="button"
                    class="toggle-btn"
                    onclick="togglePassword('confirmPassword')"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div class="error">Mật khẩu xác nhận không khớp</div>
              </div>

              <div class="form-actions">
                <button type="submit">Đăng Ký</button>
              </div>

              <div class="auth-footer">
                Đã có tài khoản? <a href="login.html">Đăng nhập ngay</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>Về Chúng Tôi</h3>
          <p>
            Cửa hàng online cung cấp các sản phẩm thời trang chất lượng cao với
            giá cả hợp lý.
          </p>
          <p>
            Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách
            hàng.
          </p>
        </div>

        <div class="footer-section">
          <h3>Liên Hệ</h3>
          <p>Email: <EMAIL></p>
          <p>Điện thoại: 0123 456 789</p>
          <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
        </div>

        <div class="footer-section">
          <h3>Liên Kết Nhanh</h3>
          <a href="index.html">Trang Chủ</a>
          <a href="product.html">Sản Phẩm</a>
          <a href="cart.html">Giỏ Hàng</a>
          <a href="login.html">Đăng Nhập</a>
          <a href="register.html">Đăng Ký</a>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2023 Cửa Hàng Online. Tất cả quyền được bảo lưu.</p>
      </div>
    </footer>

    <script src="main.js"></script>
    <script>
      // Toggle password visibility
      function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const type =
          input.getAttribute("type") === "password" ? "text" : "password";
        input.setAttribute("type", type);

        // Change eye icon
        const icon = document.querySelector(`#${inputId} ~ .toggle-btn i`);
        if (type === "text") {
          icon.classList.remove("fa-eye");
          icon.classList.add("fa-eye-slash");
        } else {
          icon.classList.remove("fa-eye-slash");
          icon.classList.add("fa-eye");
        }
      }

      // Input focus effects
      const inputs = document.querySelectorAll(".form-group input");
      inputs.forEach((input) => {
        // Add focus class on focus
        input.addEventListener("focus", function () {
          this.parentElement.classList.add("focused");
        });

        // Remove focus class on blur if input is empty
        input.addEventListener("blur", function () {
          if (this.value === "") {
            this.parentElement.classList.remove("focused");
          }
        });

        // Check if input has value on page load
        if (input.value !== "") {
          input.parentElement.classList.add("focused");
        }
      });

      // Password strength meter
      const passwordInput = document.getElementById("password");
      const strengthBar = document.getElementById("passwordStrength");

      passwordInput.addEventListener("input", function () {
        const password = this.value;
        let strength = 0;

        // Update progress bar based on password strength
        if (password.length > 0) {
          // Length check
          if (password.length >= 8) strength += 20;

          // Lowercase check
          if (/[a-z]/.test(password)) strength += 20;

          // Uppercase check
          if (/[A-Z]/.test(password)) strength += 20;

          // Number check
          if (/[0-9]/.test(password)) strength += 20;

          // Special character check
          if (/[^A-Za-z0-9]/.test(password)) strength += 20;
        }

        // Update progress bar
        strengthBar.style.width = strength + "%";

        // Update color based on strength
        if (strength <= 20) {
          strengthBar.style.background =
            "linear-gradient(to right, #ff4d4d, #ff4d4d)";
        } else if (strength <= 40) {
          strengthBar.style.background =
            "linear-gradient(to right, #ffa64d, #ffa64d)";
        } else if (strength <= 60) {
          strengthBar.style.background =
            "linear-gradient(to right, #ffff4d, #ffff4d)";
        } else if (strength <= 80) {
          strengthBar.style.background =
            "linear-gradient(to right, #4dff4d, #4dff4d)";
        } else {
          strengthBar.style.background =
            "linear-gradient(to right, #4dffff, #4d4dff)";
        }
      });

      // Form validation with animation
      document
        .getElementById("registerForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          let isValid = true;

          // Validate fullname with shake animation
          const fullname = document.getElementById("fullname");
          if (
            fullname.value.trim() === "" ||
            fullname.value.trim().length < 3
          ) {
            const fullnameGroup = fullname.parentElement;
            fullnameGroup.classList.add("error");
            fullnameGroup.style.animation = "none";
            setTimeout(() => {
              fullnameGroup.style.animation = "shake 0.5s ease";
            }, 10);
            isValid = false;
          } else {
            fullname.parentElement.classList.remove("error");
          }

          // Validate email with shake animation
          const email = document.getElementById("email");
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email.value)) {
            const emailGroup = email.parentElement;
            emailGroup.classList.add("error");
            emailGroup.style.animation = "none";
            setTimeout(() => {
              emailGroup.style.animation = "shake 0.5s ease";
            }, 10);
            isValid = false;
          } else {
            email.parentElement.classList.remove("error");
          }

          // Validate phone with shake animation
          const phone = document.getElementById("phone");
          const phoneRegex = /^[0-9]{10,11}$/;
          if (!phoneRegex.test(phone.value)) {
            const phoneGroup = phone.parentElement;
            phoneGroup.classList.add("error");
            phoneGroup.style.animation = "none";
            setTimeout(() => {
              phoneGroup.style.animation = "shake 0.5s ease";
            }, 10);
            isValid = false;
          } else {
            phone.parentElement.classList.remove("error");
          }

          // Validate password with shake animation
          const password = document.getElementById("password");
          const passwordRegex =
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/;
          if (!passwordRegex.test(password.value)) {
            const passwordGroup = password.parentElement.parentElement;
            passwordGroup.classList.add("error");
            passwordGroup.style.animation = "none";
            setTimeout(() => {
              passwordGroup.style.animation = "shake 0.5s ease";
            }, 10);
            isValid = false;
          } else {
            password.parentElement.parentElement.classList.remove("error");
          }

          // Validate confirm password with shake animation
          const confirmPassword = document.getElementById("confirmPassword");
          if (confirmPassword.value !== password.value) {
            const confirmGroup = confirmPassword.parentElement.parentElement;
            confirmGroup.classList.add("error");
            confirmGroup.style.animation = "none";
            setTimeout(() => {
              confirmGroup.style.animation = "shake 0.5s ease";
            }, 10);
            isValid = false;
          } else {
            confirmPassword.parentElement.parentElement.classList.remove(
              "error"
            );
          }

          if (isValid) {
            // Add loading state to button
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.textContent;
            button.innerHTML =
              '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
            button.disabled = true;

            // Normally would submit to server, but for demo just show success message
            setTimeout(() => {
              showNotification("Đăng ký thành công! Vui lòng đăng nhập.");
              setTimeout(() => {
                window.location.href = "login.html";
              }, 1000);
            }, 1500);
          }
        });

      // Add shake animation
      document.head.insertAdjacentHTML(
        "beforeend",
        `
        <style>
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
          }
        </style>
      `
      );
    </script>
  </body>
</html>
