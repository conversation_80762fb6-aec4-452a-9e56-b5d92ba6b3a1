/* ========================================================================== */
/* CART MANAGEMENT SYSTEM - Hệ thống quản lý giỏ hàng                       */
/* ========================================================================== */

/**
 * Lấy dữ liệu giỏ hàng từ localStorage
 * @returns {Array} Mảng chứa các sản phẩm trong giỏ hàng
 * @description Hàm này đọc dữ liệu giỏ hàng đã lưu trong localStorage,
 *              parse từ JSON string thành JavaScript object.
 *              Nếu chưa có dữ liệu thì trả về mảng rỗng.
 */
function getCart() {
  // Lấy dữ liệu từ localStorage với key "cart"
  const cartData = localStorage.getItem("cart");

  // Parse JSON string thành object, nếu null thì trả về mảng rỗng
  return cartData ? JSON.parse(cartData) : [];
}

/**
 * Lưu dữ liệu giỏ hàng vào localStorage
 * @param {Array} cart - Mảng chứa các sản phẩm trong giỏ hàng
 * @description Hàm này chuyển đổi cart object thành JSON string
 *              và lưu vào localStorage để persist data giữa các sessions
 */
function saveCart(cart) {
  // Chuyển đổi JavaScript object thành JSON string và lưu vào localStorage
  localStorage.setItem("cart", JSON.stringify(cart));
}

/**
 * Thêm sản phẩm vào giỏ hàng
 * @param {string} name - Tên sản phẩm
 * @param {number} price - Giá sản phẩm
 * @description Hàm này thêm sản phẩm mới vào giỏ hàng hoặc tăng số lượng
 *              nếu sản phẩm đã tồn tại. Sau đó lưu vào localStorage và hiển thị thông báo.
 */
function addToCart(name, price) {
  // Lấy giỏ hàng hiện tại từ localStorage
  let cart = getCart();

  // Tìm xem sản phẩm đã có trong giỏ hàng chưa
  const index = cart.findIndex((item) => item.name === name);

  if (index >= 0) {
    // Nếu sản phẩm đã tồn tại, tăng số lượng lên 1
    cart[index].quantity += 1;
  } else {
    // Nếu sản phẩm chưa có, thêm mới với quantity = 1
    cart.push({ name, price, quantity: 1 });
  }

  // Lưu giỏ hàng đã cập nhật vào localStorage
  saveCart(cart);

  // Hiển thị thông báo xác nhận cho user
  showNotification(`Đã thêm "${name}" vào giỏ hàng.`);
}

/* ========================================================================== */
/* NOTIFICATION SYSTEM - Hệ thống thông báo cho user feedback               */
/* ========================================================================== */

/**
 * Hiển thị thông báo toast notification
 * @param {string} message - Nội dung thông báo cần hiển thị
 * @description Hàm này tạo và hiển thị một toast notification ở góc phải trên màn hình.
 *              Thông báo sẽ tự động biến mất sau 3 giây hoặc khi user click nút đóng.
 *              Chỉ cho phép 1 notification hiển thị tại một thời điểm.
 */
function showNotification(message) {
  // Kiểm tra và xóa notification cũ nếu có (đảm bảo chỉ có 1 notification)
  const existingNotification = document.querySelector(".notification");
  if (existingNotification) {
    existingNotification.remove(); // Xóa notification cũ để tránh spam
  }

  // Tạo DOM element cho notification mới
  const notification = document.createElement("div");
  notification.className = "notification"; // Class để có thể query sau này

  // Tạo HTML structure với message và close button
  notification.innerHTML = `
    <div class="notification-content">
      <span>${message}</span>
      <button class="close-btn">&times;</button>
    </div>
  `;

  // === STYLING NOTIFICATION ===
  // Position fixed để notification luôn ở vị trí cố định trên viewport
  notification.style.position = "fixed";
  notification.style.top = "20px"; // Cách top 20px
  notification.style.right = "20px"; // Cách right 20px (góc phải trên)
  notification.style.backgroundColor = "var(--success-color)"; // Màu xanh success
  notification.style.color = "white"; // Text màu trắng để contrast
  notification.style.padding = "15px 20px"; // Padding để tạo breathing room
  notification.style.borderRadius = "var(--border-radius)"; // Bo góc từ CSS variables
  notification.style.boxShadow = "var(--box-shadow)"; // Đổ bóng từ CSS variables
  notification.style.zIndex = "1000"; // Z-index cao để luôn ở trên
  notification.style.transition = "all 0.3s ease"; // Smooth transition cho animations
  notification.style.opacity = "0"; // Bắt đầu với opacity 0 cho fade-in effect

  // === STYLING CLOSE BUTTON ===
  const closeBtn = notification.querySelector(".close-btn");
  closeBtn.style.background = "none"; // Không có background
  closeBtn.style.border = "none"; // Không có border
  closeBtn.style.color = "white"; // Màu trắng để match với notification
  closeBtn.style.fontSize = "20px"; // Font size lớn để dễ click
  closeBtn.style.cursor = "pointer"; // Pointer cursor để indicate clickable
  closeBtn.style.marginLeft = "10px"; // Margin left để tách khỏi message

  // Thêm notification vào DOM (vào cuối body)
  document.body.appendChild(notification);

  // === FADE-IN ANIMATION ===
  // Sử dụng setTimeout để trigger fade-in sau khi element đã được add vào DOM
  setTimeout(() => {
    notification.style.opacity = "1"; // Fade in bằng cách set opacity = 1
  }, 10); // 10ms delay để đảm bảo DOM đã update

  // === CLOSE BUTTON EVENT HANDLER ===
  closeBtn.addEventListener("click", () => {
    // Fade out animation
    notification.style.opacity = "0";
    // Xóa element sau khi animation hoàn thành
    setTimeout(() => {
      notification.remove();
    }, 300); // 300ms match với transition duration
  });

  // === AUTO-CLOSE TIMER ===
  // Tự động đóng notification sau 3 giây
  setTimeout(() => {
    // Kiểm tra xem notification còn tồn tại không (có thể đã bị đóng manual)
    if (notification.parentNode) {
      notification.style.opacity = "0"; // Fade out
      setTimeout(() => {
        notification.remove(); // Xóa khỏi DOM
      }, 300);
    }
  }, 3000); // 3000ms = 3 giây
}

/* ========================================================================== */
/* CART DISPLAY & UI MANAGEMENT - Hiển thị và quản lý giao diện giỏ hàng    */
/* ========================================================================== */

/**
 * Hiển thị nội dung giỏ hàng trong trang cart.html
 * @description Hàm này render toàn bộ giỏ hàng thành HTML table,
 *              bao gồm tên sản phẩm, giá, quantity controls, subtotal và nút xóa.
 *              Cũng tính toán và hiển thị tổng tiền.
 */
function displayCart() {
  // Lấy dữ liệu giỏ hàng từ localStorage
  const cart = getCart();

  // Lấy references đến các DOM elements cần thiết
  const cartTable = document.getElementById("cart-items");
  const cartTotal = document.getElementById("cart-total");

  // === ERROR HANDLING ===
  // Kiểm tra xem các element có tồn tại không (defensive programming)
  if (!cartTable || !cartTotal) {
    console.warn("Cart elements not found. Make sure you're on the cart page.");
    return; // Exit early nếu không tìm thấy elements
  }

  // Clear existing content để rebuild từ đầu
  cartTable.innerHTML = "";
  let total = 0; // Biến để tính tổng tiền

  // === EMPTY CART HANDLING ===
  if (cart.length === 0) {
    // Hiển thị message khi giỏ hàng trống
    cartTable.innerHTML =
      '<tr><td colspan="5" style="text-align: center; padding: 20px;">Giỏ hàng trống</td></tr>';
    cartTotal.innerText = "0₫";
    return; // Exit early khi giỏ hàng trống
  }

  // === RENDER CART ITEMS ===
  cart.forEach((item, index) => {
    // Tính subtotal cho từng item
    const subtotal = item.price * item.quantity;
    total += subtotal; // Cộng vào tổng tiền

    // Tạo table row cho mỗi item
    const row = document.createElement("tr");

    // Generate HTML cho row với dynamic content
    row.innerHTML = `
        <td>${item.name}</td>
        <td>${item.price.toLocaleString()}₫</td>
        <td>
          <button onclick="updateQuantity(${index}, ${item.quantity - 1})" ${
      item.quantity <= 1 ? "disabled" : "" // Disable nút - khi quantity = 1
    } style="${
      item.quantity <= 1 ? "opacity: 0.5; cursor: not-allowed;" : "" // Visual feedback
    }">-</button>
          ${item.quantity}
          <button onclick="updateQuantity(${index}, ${
      item.quantity + 1 // Nút + luôn enabled
    })">+</button>
        </td>
        <td>${subtotal.toLocaleString()}₫</td>
        <td><button onclick="removeFromCart(${index})">Xóa</button></td>
      `;

    // Thêm row vào table
    cartTable.appendChild(row);
  });

  // === UPDATE TOTAL DISPLAY ===
  // Format số tiền với thousands separator và thêm ký hiệu ₫
  cartTotal.innerText = total.toLocaleString() + "₫";
}

/**
 * Cập nhật số lượng sản phẩm trong giỏ hàng
 * @param {number} index - Index của sản phẩm trong mảng cart
 * @param {number} newQuantity - Số lượng mới cần cập nhật
 * @description Hàm này cập nhật số lượng sản phẩm. Nếu newQuantity <= 0,
 *              sẽ hỏi user có muốn xóa sản phẩm không. Sau khi cập nhật
 *              sẽ refresh display và hiển thị notification.
 */
function updateQuantity(index, newQuantity) {
  // === HANDLE ZERO OR NEGATIVE QUANTITY ===
  if (newQuantity <= 0) {
    // Confirm với user trước khi xóa sản phẩm
    if (confirm("Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?")) {
      removeFromCart(index); // Gọi function xóa sản phẩm
    } else {
      displayCart(); // Refresh display mà không thay đổi gì (user cancel)
    }
    return; // Exit early
  }

  // === UPDATE QUANTITY ===
  let cart = getCart(); // Lấy cart hiện tại
  cart[index].quantity = newQuantity; // Cập nhật quantity mới
  saveCart(cart); // Lưu vào localStorage
  displayCart(); // Refresh UI để hiển thị thay đổi
  showNotification("Đã cập nhật số lượng sản phẩm"); // User feedback
}

/**
 * Xóa sản phẩm khỏi giỏ hàng
 * @param {number} index - Index của sản phẩm cần xóa trong mảng cart
 * @description Hàm này xóa hoàn toàn một sản phẩm khỏi giỏ hàng,
 *              cập nhật localStorage, refresh UI và hiển thị notification.
 */
function removeFromCart(index) {
  const cart = getCart(); // Lấy cart hiện tại
  const removedItem = cart[index]; // Lưu thông tin item bị xóa để hiển thị notification

  // Xóa item khỏi mảng bằng splice (remove 1 element tại vị trí index)
  cart.splice(index, 1);

  saveCart(cart); // Lưu cart đã cập nhật vào localStorage
  displayCart(); // Refresh UI để hiển thị thay đổi

  // Hiển thị notification với tên sản phẩm đã xóa
  showNotification(`Đã xóa "${removedItem.name}" khỏi giỏ hàng`);
}

/* ========================================================================== */
/* DOM CONTENT LOADED EVENT - Khởi tạo tất cả functionality khi page load   */
/* ========================================================================== */

/**
 * Event listener chính được trigger khi DOM đã load hoàn toàn
 * @description Đây là entry point chính cho tất cả JavaScript functionality.
 *              Tất cả event listeners và initializations được setup ở đây
 *              để đảm bảo DOM elements đã sẵn sàng.
 */
document.addEventListener("DOMContentLoaded", function () {
  /* ======================================================================== */
  /* BANNER SLIDER FUNCTIONALITY - Chức năng slider cho banner chính        */
  /* ======================================================================== */

  // Lấy tất cả slides và dots từ DOM
  const slides = document.querySelectorAll(".banner-slide");
  const dots = document.querySelectorAll(".slider-dot");
  let currentSlide = 0; // Track slide hiện tại (0-based index)

  /**
   * Chuyển đổi slide đến vị trí chỉ định
   * @param {number} n - Index của slide cần chuyển đến
   * @description Hàm này remove active class từ tất cả slides/dots,
   *              sau đó add active class cho slide/dot được chỉ định.
   */
  function changeSlide(n) {
    // Remove active class từ tất cả slides và dots
    slides.forEach((slide) => slide.classList.remove("active"));
    dots.forEach((dot) => dot.classList.remove("active"));

    // Add active class cho slide và dot hiện tại
    slides[n].classList.add("active");
    dots[n].classList.add("active");

    // Update current slide index
    currentSlide = n;
  }

  /**
   * Tự động chuyển slide tiếp theo
   * @description Hàm này tính toán slide tiếp theo bằng modulo operator
   *              để loop về slide đầu tiên khi đến cuối.
   */
  function autoSlide() {
    // Tính slide tiếp theo với modulo để loop (0, 1, 2, 0, 1, 2, ...)
    currentSlide = (currentSlide + 1) % slides.length;
    changeSlide(currentSlide);
  }

  // === SETUP DOT CLICK EVENTS ===
  // Thêm click event listener cho mỗi dot
  dots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      changeSlide(index); // Chuyển đến slide tương ứng với dot được click
    });
  });

  // === AUTO SLIDE TIMER ===
  // Khởi tạo interval để tự động chuyển slide mỗi 5 giây
  let slideInterval = setInterval(autoSlide, 5000);

  // === PAUSE ON HOVER FUNCTIONALITY ===
  const slider = document.querySelector(".banner-slider");
  if (slider) {
    // Pause auto slide khi user hover vào slider
    slider.addEventListener("mouseenter", () => {
      clearInterval(slideInterval); // Dừng auto slide
    });

    // Resume auto slide khi user rời khỏi slider
    slider.addEventListener("mouseleave", () => {
      slideInterval = setInterval(autoSlide, 5000); // Restart auto slide
    });
  }

  /* ======================================================================== */
  /* CATEGORY FILTER FUNCTIONALITY - Chức năng filter sản phẩm theo category */
  /* ======================================================================== */

  // Lấy tất cả category buttons từ DOM
  const categoryButtons = document.querySelectorAll(".category-btn");

  /**
   * Setup event listeners cho category buttons
   * @description Mỗi category button khi được click sẽ trở thành active
   *              và tất cả buttons khác sẽ bị remove active class.
   *              Tạo exclusive selection behavior.
   */
  categoryButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Remove active class từ tất cả buttons (reset state)
      categoryButtons.forEach((btn) => btn.classList.remove("active"));

      // Add active class cho button được click (highlight selection)
      this.classList.add("active");

      // Note: Actual filtering logic có thể được thêm ở đây
      // Ví dụ: filterProductsByCategory(this.dataset.category);
    });
  });

  /* ======================================================================== */
  /* WISHLIST FUNCTIONALITY - Chức năng thêm/xóa sản phẩm yêu thích         */
  /* ======================================================================== */

  // Lấy tất cả wishlist buttons từ DOM
  const wishlistButtons = document.querySelectorAll(".wishlist-btn");

  /**
   * Setup event listeners cho wishlist buttons
   * @description Mỗi wishlist button toggle giữa 2 states: added và removed.
   *              Visual feedback bằng cách thay đổi icon, màu sắc và hiển thị notification.
   */
  wishlistButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      // Prevent default behavior (tránh navigation nếu button trong link)
      e.preventDefault();

      // === TOGGLE WISHLIST STATE ===
      // Check current state bằng text content của button
      if (this.textContent === "❤") {
        // === ADD TO WISHLIST STATE ===
        this.textContent = "♥"; // Filled heart icon
        this.style.backgroundColor = "var(--secondary-color)"; // Highlight background
        this.style.color = "white"; // White text cho contrast
        showNotification("Đã thêm vào danh sách yêu thích"); // User feedback

        // Note: Actual wishlist logic có thể được thêm ở đây
        // Ví dụ: addToWishlist(this.dataset.productId);
      } else {
        // === REMOVE FROM WISHLIST STATE ===
        this.textContent = "❤"; // Empty heart icon
        this.style.backgroundColor = "var(--bg-dark)"; // Default background
        this.style.color = "var(--text-color)"; // Default text color
        showNotification("Đã xóa khỏi danh sách yêu thích"); // User feedback

        // Note: Actual wishlist logic có thể được thêm ở đây
        // Ví dụ: removeFromWishlist(this.dataset.productId);
      }
    });
  });

  // === END OF DOM CONTENT LOADED EVENT ===
});
