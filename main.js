// Hàm lấy giỏ hàng từ localStorage
function getCart() {
  return JSON.parse(localStorage.getItem("cart")) || [];
}

// Hàm lưu giỏ hàng vào localStorage
function saveCart(cart) {
  localStorage.setItem("cart", JSON.stringify(cart));
}

// Thêm sản phẩm vào giỏ hàng
function addToCart(name, price) {
  let cart = getCart();
  const index = cart.findIndex((item) => item.name === name);

  if (index >= 0) {
    cart[index].quantity += 1;
  } else {
    cart.push({ name, price, quantity: 1 });
  }

  saveCart(cart);

  // Hiển thị thông báo thêm vào giỏ hàng
  showNotification(`Đã thêm "${name}" vào giỏ hàng.`);
}

// Hiển thị thông báo
function showNotification(message) {
  // Kiểm tra xem đã có thông báo nào chưa
  const existingNotification = document.querySelector(".notification");
  if (existingNotification) {
    existingNotification.remove();
  }

  // Tạo thông báo mới
  const notification = document.createElement("div");
  notification.className = "notification";
  notification.innerHTML = `
    <div class="notification-content">
      <span>${message}</span>
      <button class="close-btn">&times;</button>
    </div>
  `;

  // Thêm style cho thông báo
  notification.style.position = "fixed";
  notification.style.top = "20px";
  notification.style.right = "20px";
  notification.style.backgroundColor = "var(--success-color)";
  notification.style.color = "white";
  notification.style.padding = "15px 20px";
  notification.style.borderRadius = "var(--border-radius)";
  notification.style.boxShadow = "var(--box-shadow)";
  notification.style.zIndex = "1000";
  notification.style.transition = "all 0.3s ease";
  notification.style.opacity = "0";

  // Thêm style cho nút đóng
  const closeBtn = notification.querySelector(".close-btn");
  closeBtn.style.background = "none";
  closeBtn.style.border = "none";
  closeBtn.style.color = "white";
  closeBtn.style.fontSize = "20px";
  closeBtn.style.cursor = "pointer";
  closeBtn.style.marginLeft = "10px";

  // Thêm thông báo vào body
  document.body.appendChild(notification);

  // Hiệu ứng hiển thị
  setTimeout(() => {
    notification.style.opacity = "1";
  }, 10);

  // Đóng thông báo khi click vào nút đóng
  closeBtn.addEventListener("click", () => {
    notification.style.opacity = "0";
    setTimeout(() => {
      notification.remove();
    }, 300);
  });

  // Tự động đóng sau 3 giây
  setTimeout(() => {
    notification.style.opacity = "0";
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}

// Hiển thị giỏ hàng (dùng trong cart.html)
function displayCart() {
  const cart = getCart();
  const cartTable = document.getElementById("cart-items");
  const cartTotal = document.getElementById("cart-total");

  // Kiểm tra xem các element có tồn tại không
  if (!cartTable || !cartTotal) {
    console.warn("Cart elements not found. Make sure you're on the cart page.");
    return;
  }

  cartTable.innerHTML = "";
  let total = 0;

  if (cart.length === 0) {
    cartTable.innerHTML =
      '<tr><td colspan="5" style="text-align: center; padding: 20px;">Giỏ hàng trống</td></tr>';
    cartTotal.innerText = "0₫";
    return;
  }

  cart.forEach((item, index) => {
    const subtotal = item.price * item.quantity;
    total += subtotal;

    const row = document.createElement("tr");
    row.innerHTML = `
        <td>${item.name}</td>
        <td>${item.price.toLocaleString()}₫</td>
        <td>
          <button onclick="updateQuantity(${index}, ${item.quantity - 1})" ${
      item.quantity <= 1 ? "disabled" : ""
    } style="${
      item.quantity <= 1 ? "opacity: 0.5; cursor: not-allowed;" : ""
    }">-</button>
          ${item.quantity}
          <button onclick="updateQuantity(${index}, ${
      item.quantity + 1
    })">+</button>
        </td>
        <td>${subtotal.toLocaleString()}₫</td>
        <td><button onclick="removeFromCart(${index})">Xóa</button></td>
      `;
    cartTable.appendChild(row);
  });

  cartTotal.innerText = total.toLocaleString() + "₫";
}

// Cập nhật số lượng sản phẩm
function updateQuantity(index, newQuantity) {
  if (newQuantity <= 0) {
    if (confirm("Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?")) {
      removeFromCart(index);
    } else {
      displayCart(); // Refresh display without changes
    }
    return;
  }

  let cart = getCart();
  cart[index].quantity = newQuantity;
  saveCart(cart);
  displayCart();
  showNotification("Đã cập nhật số lượng sản phẩm");
}

// Xóa sản phẩm khỏi giỏ hàng
function removeFromCart(index) {
  const cart = getCart();
  const removedItem = cart[index];
  cart.splice(index, 1);
  saveCart(cart);
  displayCart();
  showNotification(`Đã xóa "${removedItem.name}" khỏi giỏ hàng`);
}

// Slider functionality
document.addEventListener("DOMContentLoaded", function () {
  // Slider functionality
  const slides = document.querySelectorAll(".banner-slide");
  const dots = document.querySelectorAll(".slider-dot");
  let currentSlide = 0;

  // Function to change slide
  function changeSlide(n) {
    // Remove active class from all slides and dots
    slides.forEach((slide) => slide.classList.remove("active"));
    dots.forEach((dot) => dot.classList.remove("active"));

    // Add active class to current slide and dot
    slides[n].classList.add("active");
    dots[n].classList.add("active");

    currentSlide = n;
  }

  // Auto slide change
  function autoSlide() {
    currentSlide = (currentSlide + 1) % slides.length;
    changeSlide(currentSlide);
  }

  // Set up click events for dots
  dots.forEach((dot, index) => {
    dot.addEventListener("click", () => {
      changeSlide(index);
    });
  });

  // Start auto slide
  let slideInterval = setInterval(autoSlide, 5000);

  // Pause auto slide on hover
  const slider = document.querySelector(".banner-slider");
  if (slider) {
    slider.addEventListener("mouseenter", () => {
      clearInterval(slideInterval);
    });

    slider.addEventListener("mouseleave", () => {
      slideInterval = setInterval(autoSlide, 5000);
    });
  }

  // Category buttons functionality
  const categoryButtons = document.querySelectorAll(".category-btn");

  categoryButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Remove active class from all buttons
      categoryButtons.forEach((btn) => btn.classList.remove("active"));

      // Add active class to clicked button
      this.classList.add("active");
    });
  });

  // Add wishlist functionality
  const wishlistButtons = document.querySelectorAll(".wishlist-btn");

  wishlistButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Toggle heart fill/empty
      if (this.textContent === "❤") {
        this.textContent = "♥";
        this.style.backgroundColor = "var(--secondary-color)";
        this.style.color = "white";
        showNotification("Đã thêm vào danh sách yêu thích");
      } else {
        this.textContent = "❤";
        this.style.backgroundColor = "var(--bg-dark)";
        this.style.color = "var(--text-color)";
        showNotification("Đã xóa khỏi danh sách yêu thích");
      }
    });
  });
});
