<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Cửa hàng đổi điểm - <PERSON><PERSON> dụng điểm tích lũy để đổi lấy các phần thưởng hấp dẫn">
    <title>Cửa Hàng <PERSON>ể<PERSON> - Rewards Store</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .rewards-store {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .store-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .points-balance {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }

        .points-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: var(--spacing-sm);
        }

        .rewards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .reward-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .reward-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: var(--box-shadow);
        }

        .reward-card.featured {
            border-color: var(--accent-color);
            background: linear-gradient(135deg, #fff, #f8f9ff);
        }

        .reward-card.featured::before {
            content: '⭐ FEATURED';
            position: absolute;
            top: 10px;
            right: -30px;
            background: var(--accent-color);
            color: white;
            padding: 5px 40px;
            font-size: 0.8rem;
            font-weight: 600;
            transform: rotate(45deg);
        }

        .reward-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md);
        }

        .reward-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            color: var(--text-color);
        }

        .reward-description {
            color: var(--text-light);
            margin-bottom: var(--spacing-md);
            line-height: 1.5;
        }

        .reward-cost {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .reward-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius);
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            width: 100%;
        }

        .reward-button:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .reward-button:disabled {
            background: var(--text-light);
            cursor: not-allowed;
            transform: none;
        }

        .reward-button.claimed {
            background: var(--success-color);
            cursor: default;
        }

        .reward-button.claimed:hover {
            transform: none;
        }

        .purchase-history {
            background: var(--bg-light);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--border-color);
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-date {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .achievement-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .achievement-item {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-md);
            text-align: center;
            transition: var(--transition);
        }

        .achievement-item.unlocked {
            border-color: var(--accent-color);
            background: linear-gradient(135deg, #fff, #fff8e1);
        }

        .achievement-item.locked {
            opacity: 0.5;
            filter: grayscale(100%);
        }

        .achievement-icon {
            font-size: 2rem;
            margin-bottom: var(--spacing-xs);
        }

        .achievement-name {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: var(--spacing-xs);
        }

        .achievement-desc {
            font-size: 0.8rem;
            color: var(--text-light);
        }

        @media (max-width: 768px) {
            .rewards-store {
                padding: var(--spacing-lg);
            }

            .rewards-grid {
                grid-template-columns: 1fr;
            }

            .points-value {
                font-size: 2rem;
            }

            .achievement-showcase {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <header>
        <h1><a href="index.html">Cửa Hàng Online</a></h1>
        <nav>
            <a href="index.html">Trang Chủ</a>
            <a href="product.html">Sản Phẩm</a>
            <a href="cart.html">Giỏ Hàng</a>
            <a href="rewards-store.html" class="active">Đổi Điểm</a>
            <a href="login.html">Đăng Nhập</a>
        </nav>
    </header>

    <main class="rewards-store">
        <div class="store-header">
            <h1>🏆 Cửa Hàng Đổi Điểm</h1>
            <p>Sử dụng điểm tích lũy từ việc chơi game để đổi lấy các phần thưởng hấp dẫn!</p>
        </div>

        <div class="points-balance">
            <div class="points-value" id="userPoints">0</div>
            <div>Điểm hiện có</div>
        </div>

        <div class="rewards-grid" id="rewardsGrid">
            <!-- Rewards will be generated by JavaScript -->
        </div>

        <div class="purchase-history">
            <h3>📋 Lịch Sử Đổi Điểm</h3>
            <div id="purchaseHistory">
                <p style="text-align: center; color: var(--text-light); padding: var(--spacing-lg);">
                    Chưa có giao dịch nào
                </p>
            </div>
        </div>

        <div class="achievement-showcase">
            <h3 style="grid-column: 1 / -1; text-align: center; margin-bottom: var(--spacing-md);">
                🏅 Thành Tích Đã Mở Khóa
            </h3>
            <div id="achievementShowcase">
                <!-- Achievements will be generated by JavaScript -->
            </div>
        </div>
    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>Liên Hệ</h3>
                <p>📧 <EMAIL></p>
                <p>📞 1900-1234</p>
                <p>📍 123 Đường ABC, TP.HCM</p>
            </div>
            <div class="footer-section">
                <h3>Chính Sách</h3>
                <a href="#">Chính sách bảo mật</a>
                <a href="#">Điều khoản sử dụng</a>
                <a href="#">Chính sách đổi trả</a>
            </div>
            <div class="footer-section">
                <h3>Hỗ Trợ</h3>
                <a href="#">Hướng dẫn mua hàng</a>
                <a href="#">Phương thức thanh toán</a>
                <a href="#">Vận chuyển</a>
            </div>
            <div class="footer-section">
                <h3>Theo Dõi</h3>
                <a href="#">Facebook</a>
                <a href="#">Instagram</a>
                <a href="#">YouTube</a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 Cửa Hàng Online. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script src="main.js"></script>
    <script src="game-engine.js"></script>
    <script>
        // Rewards Store JavaScript
        const rewardsData = [
            {
                id: 'discount5',
                icon: '🎫',
                title: 'Mã Giảm Giá 5%',
                description: 'Áp dụng cho tất cả sản phẩm, không giới hạn giá trị đơn hàng',
                cost: 50,
                code: 'POINTS5',
                featured: false,
                category: 'discount'
            },
            {
                id: 'discount10',
                icon: '🎟️',
                title: 'Mã Giảm Giá 10%',
                description: 'Áp dụng cho đơn hàng từ 500.000₫ trở lên',
                cost: 100,
                code: 'POINTS10',
                featured: true,
                category: 'discount'
            },
            {
                id: 'freeship',
                icon: '🚚',
                title: 'Miễn Phí Vận Chuyển',
                description: 'Miễn phí ship cho đơn hàng bất kỳ trong 30 ngày',
                cost: 75,
                code: 'FREESHIP30',
                featured: false,
                category: 'shipping'
            },
            {
                id: 'discount20',
                icon: '🎁',
                title: 'Mã Giảm Giá 20%',
                description: 'Áp dụng cho đơn hàng từ 1.000.000₫, giới hạn 1 lần/tháng',
                cost: 200,
                code: 'POINTS20',
                featured: true,
                category: 'discount'
            },
            {
                id: 'earlyaccess',
                icon: '⚡',
                title: 'Early Access Sale',
                description: 'Được mua hàng trước 24h khi có sale lớn',
                cost: 150,
                code: 'EARLY_ACCESS',
                featured: false,
                category: 'special'
            },
            {
                id: 'vip',
                icon: '👑',
                title: 'VIP Membership 1 Tháng',
                description: 'Ưu tiên hỗ trợ, giảm giá độc quyền, freeship không giới hạn',
                cost: 300,
                code: 'VIP_MONTH',
                featured: true,
                category: 'membership'
            }
        ];

        function loadUserData() {
            const checkinData = JSON.parse(localStorage.getItem('checkinData') || '{"totalPoints": 0}');
            const purchaseHistory = JSON.parse(localStorage.getItem('purchaseHistory') || '[]');
            const achievements = JSON.parse(localStorage.getItem('achievements') || '[]');
            
            return {
                points: checkinData.totalPoints || 0,
                history: purchaseHistory,
                achievements: achievements
            };
        }

        function saveUserData(data) {
            const checkinData = JSON.parse(localStorage.getItem('checkinData') || '{}');
            checkinData.totalPoints = data.points;
            localStorage.setItem('checkinData', JSON.stringify(checkinData));
            localStorage.setItem('purchaseHistory', JSON.stringify(data.history));
        }

        function renderRewards() {
            const userData = loadUserData();
            const grid = document.getElementById('rewardsGrid');
            
            grid.innerHTML = rewardsData.map(reward => {
                const canAfford = userData.points >= reward.cost;
                const alreadyClaimed = userData.history.some(item => item.rewardId === reward.id && 
                    new Date(item.date).toDateString() === new Date().toDateString());
                
                let buttonText = 'Đổi Ngay';
                let buttonClass = 'reward-button';
                let disabled = '';
                
                if (alreadyClaimed) {
                    buttonText = '✓ Đã Đổi Hôm Nay';
                    buttonClass += ' claimed';
                    disabled = 'disabled';
                } else if (!canAfford) {
                    buttonText = 'Không Đủ Điểm';
                    disabled = 'disabled';
                }
                
                return `
                    <div class="reward-card ${reward.featured ? 'featured' : ''}">
                        <div class="reward-icon">${reward.icon}</div>
                        <div class="reward-title">${reward.title}</div>
                        <div class="reward-description">${reward.description}</div>
                        <div class="reward-cost">${reward.cost} điểm</div>
                        <button class="${buttonClass}" onclick="purchaseReward('${reward.id}')" ${disabled}>
                            ${buttonText}
                        </button>
                    </div>
                `;
            }).join('');
        }

        function purchaseReward(rewardId) {
            const userData = loadUserData();
            const reward = rewardsData.find(r => r.id === rewardId);
            
            if (!reward) return;
            
            // Check if already claimed today
            const alreadyClaimed = userData.history.some(item => 
                item.rewardId === rewardId && 
                new Date(item.date).toDateString() === new Date().toDateString()
            );
            
            if (alreadyClaimed) {
                showNotification('Bạn đã đổi phần thưởng này hôm nay rồi!');
                return;
            }
            
            if (userData.points < reward.cost) {
                showNotification('Không đủ điểm để đổi phần thưởng này!');
                return;
            }
            
            // Deduct points and add to history
            userData.points -= reward.cost;
            userData.history.unshift({
                rewardId: reward.id,
                title: reward.title,
                cost: reward.cost,
                code: reward.code,
                date: new Date().toISOString()
            });
            
            saveUserData(userData);
            
            // Copy code to clipboard
            if (navigator.clipboard && reward.code) {
                navigator.clipboard.writeText(reward.code);
            }
            
            showNotification(`🎉 Đổi thành công! Mã: ${reward.code}`);
            gameEngine.playSound('win');
            gameEngine.createConfetti();
            
            // Refresh display
            updatePointsDisplay();
            renderRewards();
            renderPurchaseHistory();
        }

        function updatePointsDisplay() {
            const userData = loadUserData();
            document.getElementById('userPoints').textContent = userData.points;
        }

        function renderPurchaseHistory() {
            const userData = loadUserData();
            const container = document.getElementById('purchaseHistory');
            
            if (userData.history.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: var(--text-light); padding: var(--spacing-lg);">Chưa có giao dịch nào</p>';
                return;
            }
            
            container.innerHTML = userData.history.slice(0, 10).map(item => `
                <div class="history-item">
                    <div>
                        <div style="font-weight: 600;">${item.title}</div>
                        <div style="font-size: 0.9rem; color: var(--text-light);">Mã: ${item.code}</div>
                    </div>
                    <div style="text-align: right;">
                        <div style="color: var(--primary-color); font-weight: 600;">-${item.cost} điểm</div>
                        <div class="history-date">${new Date(item.date).toLocaleDateString('vi-VN')}</div>
                    </div>
                </div>
            `).join('');
        }

        function renderAchievements() {
            const userData = loadUserData();
            const container = document.getElementById('achievementShowcase');
            
            const achievementDefs = gameEngine.achievementDefinitions;
            
            container.innerHTML = Object.keys(achievementDefs).map(key => {
                const achievement = achievementDefs[key];
                const unlocked = userData.achievements.includes(key);
                
                return `
                    <div class="achievement-item ${unlocked ? 'unlocked' : 'locked'}">
                        <div class="achievement-icon">${achievement.icon}</div>
                        <div class="achievement-name">${achievement.name}</div>
                        <div class="achievement-desc">${achievement.description}</div>
                    </div>
                `;
            }).join('');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updatePointsDisplay();
            renderRewards();
            renderPurchaseHistory();
            renderAchievements();
        });
    </script>
</body>
</html>
