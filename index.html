<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Cửa hàng online cung cấp các sản phẩm thời trang chất lượng cao với giá cả hợp lý. Miễn phí vận chuyển cho đơn hàng trên 500k."
    />
    <meta
      name="keywords"
      content="thời trang, quần áo, gi<PERSON><PERSON> dép, túi xách, phụ kiện, online shopping"
    />
    <meta name="author" content="Cửa Hàng Online" />
    <title>Trang Chủ - Cửa Hàng Online | Thời Trang Chất Lượng</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <!-- Promotion Banner -->
    <div class="promo-banner" id="promoBanner">
      🎉 <PERSON>ễn phí vận chuyển cho đơn hàng trên 500k | Giảm 20% cho khách hàng
      mới với mã: NEW20
      <button class="close-btn" onclick="closePromoBanner()">&times;</button>
    </div>

    <!-- Pop-up Modal -->
    <div class="popup-overlay" id="promoPopup">
      <div class="popup-content">
        <button class="popup-close" onclick="closePopup()">&times;</button>
        <h2 class="popup-title">🎁 Ưu Đãi Đặc Biệt!</h2>
        <p>Đăng ký ngay để nhận mã giảm giá 15% cho lần mua đầu tiên!</p>
        <div class="popup-discount">SAVE15</div>
        <p>Áp dụng cho tất cả sản phẩm. Có hiệu lực trong 7 ngày.</p>
        <div class="popup-actions">
          <button class="btn-primary" onclick="claimDiscount()">
            Nhận Ngay
          </button>
          <button class="btn-secondary" onclick="closePopup()">Để Sau</button>
        </div>
        <label style="margin-top: 15px; display: block; font-size: 0.9rem">
          <input type="checkbox" id="dontShowAgain" /> Không hiển thị lại
        </label>
      </div>
    </div>

    <!-- Social Proof Notification -->
    <div class="social-proof" id="socialProof">
      <div class="social-proof-content">
        <img
          src="https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3"
          alt="Customer"
          class="social-proof-avatar"
        />
        <div class="social-proof-text">
          <div class="social-proof-name">Nguyễn Thị Mai</div>
          <div class="social-proof-action">vừa mua Áo thun nam</div>
        </div>
      </div>
    </div>

    <!-- Sound Toggle -->
    <button
      class="sound-toggle"
      onclick="toggleSound()"
      id="soundToggle"
      title="Toggle Sound"
    >
      🔊
    </button>

    <!-- Games Button -->
    <button class="games-button" onclick="openGamesModal()">
      🎮 Mini Games
    </button>

    <header>
      <h1>Cửa Hàng Online</h1>
      <nav>
        <a href="index.html">Trang Chủ</a>
        <a href="product.html">Sản Phẩm</a>
        <a href="cart.html">Giỏ Hàng</a>
        <a href="rewards-store.html">🏆 Đổi Điểm</a>
        <a href="login.html">Đăng Nhập</a>
        <a href="register.html">Đăng Ký</a>
      </nav>
    </header>
    <main>
      <!-- Banner/Slider -->
      <div class="banner-slider">
        <div
          class="banner-slide active"
          style="
            background-image: url('https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
          "
        >
          <div class="banner-content">
            <h2>Bộ Sưu Tập Mới Nhất</h2>
            <p>
              Khám phá các xu hướng thời trang mới nhất với bộ sưu tập mùa hè
              2023.
            </p>
            <a href="product.html" class="btn">Xem Ngay</a>
          </div>
        </div>
        <div
          class="banner-slide"
          style="
            background-image: url('https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
          "
        >
          <div class="banner-content">
            <h2>Giảm Giá Đến 50%</h2>
            <p>
              Ưu đãi đặc biệt cho tất cả các sản phẩm trong danh mục giày dép và
              túi xách.
            </p>
            <a href="product.html" class="btn">Mua Ngay</a>
          </div>
        </div>
        <div
          class="banner-slide"
          style="
            background-image: url('https://images.unsplash.com/photo-1445205170230-053b83016050?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1171&q=80');
          "
        >
          <div class="banner-content">
            <h2>Phong Cách Thời Thượng</h2>
            <p>
              Tự tin tỏa sáng với những bộ trang phục được thiết kế độc quyền.
            </p>
            <a href="product.html" class="btn">Khám Phá</a>
          </div>
        </div>
        <div class="slider-controls">
          <div class="slider-dot active"></div>
          <div class="slider-dot"></div>
          <div class="slider-dot"></div>
        </div>
      </div>

      <!-- Store Introduction -->
      <div class="store-intro">
        <h2>Chào Mừng Đến Với Cửa Hàng Online</h2>
        <p>
          Chúng tôi cung cấp các sản phẩm thời trang chất lượng cao với giá cả
          hợp lý. Với hơn 10 năm kinh nghiệm trong ngành, chúng tôi tự hào mang
          đến cho khách hàng những trải nghiệm mua sắm tuyệt vời nhất.
        </p>
        <p>
          Tất cả sản phẩm đều được lựa chọn kỹ lưỡng, đảm bảo chất lượng và
          phong cách thời thượng.
        </p>

        <div class="store-features">
          <div class="feature">
            <i>🚚</i>
            <h3>Giao Hàng Miễn Phí</h3>
            <p>Miễn phí giao hàng cho tất cả đơn hàng trên 500.000₫</p>
          </div>
          <div class="feature">
            <i>🔄</i>
            <h3>Đổi Trả Dễ Dàng</h3>
            <p>Chính sách đổi trả trong vòng 30 ngày</p>
          </div>
          <div class="feature">
            <i>🔒</i>
            <h3>Thanh Toán An Toàn</h3>
            <p>Nhiều phương thức thanh toán bảo mật</p>
          </div>
          <div class="feature">
            <i>💬</i>
            <h3>Hỗ Trợ 24/7</h3>
            <p>Luôn sẵn sàng hỗ trợ mọi lúc bạn cần</p>
          </div>
        </div>
      </div>

      <!-- Product Categories -->
      <div class="section-header">
        <h2>Danh Mục Sản Phẩm</h2>
        <a href="product.html" class="view-all">Xem tất cả <i>→</i></a>
      </div>

      <div class="product-categories">
        <button class="category-btn active">Tất cả</button>
        <button class="category-btn">Thời trang nam</button>
        <button class="category-btn">Thời trang nữ</button>
        <button class="category-btn">Giày dép</button>
        <button class="category-btn">Phụ kiện</button>
        <button class="category-btn">Túi xách</button>
        <button class="category-btn">Đồng hồ</button>
      </div>

      <!-- Featured Products -->
      <div class="section-header">
        <h2>Sản Phẩm Nổi Bật</h2>
        <a href="product.html" class="view-all">Xem tất cả <i>→</i></a>
      </div>
      <div class="product-grid">
        <!-- Sản phẩm nổi bật -->
        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=800&auto=format&fit=crop"
            alt="Áo thun nam"
            loading="lazy"
          />
          <h3><a href="product.html">Áo thun nam</a></h3>
          <div class="product-price">
            <span class="current-price">200,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo thun nam', 200000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1542272604-787c3835535d?q=80&w=800&auto=format&fit=crop"
            alt="Quần jeans nữ"
          />
          <h3><a href="product.html">Quần jeans nữ</a></h3>
          <div class="product-price">
            <span class="current-price">350,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Quần jeans nữ', 350000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <img
            src="https://images.unsplash.com/photo-1598033129183-c4f50c736f10?q=80&w=800&auto=format&fit=crop"
            alt="Áo sơ mi trắng"
          />
          <h3><a href="product.html">Áo sơ mi trắng</a></h3>
          <div class="product-price">
            <span class="current-price">250,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo sơ mi trắng', 250000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?q=80&w=800&auto=format&fit=crop"
            alt="Váy mùa hè"
          />
          <h3><a href="product.html">Đồ thể thao cho nữ</a></h3>
          <div class="product-price">
            <span class="current-price">300,000₫</span>
            <span class="old-price">380,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Váy mùa hè', 300000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105?q=80&w=800&auto=format&fit=crop"
            alt="Áo blazer nữ"
          />
          <h3><a href="product.html">Áo blazer nữ</a></h3>
          <div class="product-price">
            <span class="current-price">650,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo blazer nữ', 650000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1571945153237-4929e783af4a?q=80&w=800&auto=format&fit=crop"
            alt="Quần short jean"
          />
          <h3><a href="product.html">Áo thun nam hở tay</a></h3>
          <div class="product-price">
            <span class="current-price">280,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Quần short jean', 280000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>
      </div>

      <!-- New Arrivals -->
      <div class="section-header">
        <h2>Sản Phẩm Mới</h2>
        <a href="product.html" class="view-all">Xem tất cả <i>→</i></a>
      </div>

      <div class="product-grid">
        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1543163521-1bf539c55dd2?q=80&w=800&auto=format&fit=crop"
            alt="Giày thể thao"
          />
          <h3><a href="product.html">Giày cao gót</a></h3>
          <div class="product-price">
            <span class="current-price">500,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Giày thể thao', 500000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1560769629-975ec94e6a86?q=80&w=800&auto=format&fit=crop"
            alt="Túi xách nữ"
          />
          <h3><a href="product.html">Giày thể thao</a></h3>
          <div class="product-price">
            <span class="current-price">450,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Túi xách nữ', 450000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1591047139829-d91aecb6caea?q=80&w=800&auto=format&fit=crop"
            alt="Áo hoodie"
          />
          <h3><a href="product.html">Áo khoác dù</a></h3>
          <div class="product-price">
            <span class="current-price">400,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo hoodie', 400000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1553062407-98eeb64c6a62?q=80&w=800&auto=format&fit=crop"
            alt="Balo học sinh"
          />
          <h3><a href="product.html">Balo học sinh</a></h3>
          <div class="product-price">
            <span class="current-price">320,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Balo học sinh', 320000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?q=80&w=800&auto=format&fit=crop"
            alt="Áo len cổ lọ"
            loading="lazy"
          />
          <h3><a href="product.html">Áo thun ngắn tay</a></h3>
          <div class="product-price">
            <span class="current-price">480,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo len cổ lọ', 480000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-new">Mới</span>
          <img
            src="https://images.unsplash.com/photo-1549298916-b41d501d3772?q=80&w=800&auto=format&fit=crop"
            alt="Giày sneaker trắng"
          />
          <h3><a href="product.html">Giày sneaker</a></h3>
          <div class="product-price">
            <span class="current-price">750,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Giày sneaker trắng', 750000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>
      </div>

      <!-- Countdown Timer -->
      <div class="countdown-section">
        <h2 class="countdown-title">⚡ Flash Sale - Kết thúc trong:</h2>
        <div class="countdown-timer" id="countdownTimer">
          <div class="countdown-item">
            <span class="countdown-number" id="days">00</span>
            <span class="countdown-label">Ngày</span>
          </div>
          <div class="countdown-item">
            <span class="countdown-number" id="hours">00</span>
            <span class="countdown-label">Giờ</span>
          </div>
          <div class="countdown-item">
            <span class="countdown-number" id="minutes">00</span>
            <span class="countdown-label">Phút</span>
          </div>
          <div class="countdown-item">
            <span class="countdown-number" id="seconds">00</span>
            <span class="countdown-label">Giây</span>
          </div>
        </div>
        <p>Giảm giá lên đến 50% cho các sản phẩm được chọn!</p>
      </div>

      <!-- Best Sellers -->
      <div class="section-header">
        <h2>Sản Phẩm Bán Chạy</h2>
        <a href="product.html" class="view-all">Xem tất cả <i>→</i></a>
      </div>

      <div class="product-grid">
        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1524805444758-089113d48a6d?q=80&w=800&auto=format&fit=crop"
            alt="Đồng hồ đeo tay"
          />
          <h3><a href="product.html">Đồng hồ đeo tay</a></h3>
          <div class="product-price">
            <span class="current-price">600,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Đồng hồ đeo tay', 600000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1617038260897-41a1f14a8ca0?q=80&w=800&auto=format&fit=crop"
            alt="Kính mát thời trang"
          />
          <h3><a href="product.html">Bông tai thời trang</a></h3>
          <div class="product-price">
            <span class="current-price">280,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Kính mát thời trang', 280000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1611312449408-fcece27cdbb7?q=80&w=800&auto=format&fit=crop"
            alt="Mũ lưỡi trai"
          />
          <h3><a href="product.html">Áo khoác lưỡi trai</a></h3>
          <div class="product-price">
            <span class="current-price">180,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Mũ lưỡi trai', 180000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1523381210434-271e8be1f52b?q=80&w=800&auto=format&fit=crop"
            alt="Áo khoác jean"
          />
          <h3><a href="product.html">Áo thun nam/a></h3>
          <div class="product-price">
            <span class="current-price">420,000₫</span>
            <span class="old-price">550,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo khoác jean', 420000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-hot">Hot</span>
          <img
            src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?q=80&w=800&auto=format&fit=crop"
            alt="Áo polo nam"
          />
          <h3><a href="product.html">Áo giữ nhiệt/a></h3>
          <div class="product-price">
            <span class="current-price">350,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Áo polo nam', 350000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>

        <div class="product">
          <span class="product-badge badge-sale">Sale</span>
          <img
            src="https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?q=80&w=800&auto=format&fit=crop"
            alt="Váy công sở"
          />
          <h3><a href="product.html">Quần jean màu</a></h3>
          <div class="product-price">
            <span class="current-price">520,000₫</span>
            <span class="old-price">650,000₫</span>
          </div>
          <div class="product-actions">
            <button onclick="addToCart('Váy công sở', 520000)">
              Thêm vào giỏ
            </button>
            <button class="wishlist-btn">❤</button>
          </div>
        </div>
      </div>

      <!-- Testimonials -->
      <div class="testimonials-section">
        <div class="section-header">
          <h2>Khách Hàng Nói Gì Về Chúng Tôi</h2>
        </div>
        <div class="testimonials-container">
          <div class="testimonial active">
            <img
              src="https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=80&h=80&auto=format&fit=crop&ixlib=rb-4.0.3"
              alt="Customer 1"
              class="testimonial-avatar"
            />
            <div class="testimonial-rating">★★★★★</div>
            <p class="testimonial-text">
              "Sản phẩm chất lượng tuyệt vời, giao hàng nhanh chóng. Tôi rất hài
              lòng với dịch vụ của cửa hàng!"
            </p>
            <div class="testimonial-author">Nguyễn Thị Mai - TP.HCM</div>
          </div>

          <div class="testimonial">
            <img
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=80&h=80&auto=format&fit=crop&ixlib=rb-4.0.3"
              alt="Customer 2"
              class="testimonial-avatar"
            />
            <div class="testimonial-rating">★★★★★</div>
            <p class="testimonial-text">
              "Thiết kế đẹp, chất liệu tốt, giá cả hợp lý. Đây sẽ là nơi tôi mua
              sắm thường xuyên!"
            </p>
            <div class="testimonial-author">Trần Văn Nam - Hà Nội</div>
          </div>

          <div class="testimonial">
            <img
              src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=80&h=80&auto=format&fit=crop&ixlib=rb-4.0.3"
              alt="Customer 3"
              class="testimonial-avatar"
            />
            <div class="testimonial-rating">★★★★★</div>
            <p class="testimonial-text">
              "Dịch vụ khách hàng tuyệt vời, hỗ trợ nhiệt tình. Sản phẩm đúng
              như mô tả, rất đáng tin cậy!"
            </p>
            <div class="testimonial-author">Lê Thị Hoa - Đà Nẵng</div>
          </div>

          <div class="testimonial-nav">
            <span
              class="testimonial-dot active"
              onclick="showTestimonial(0)"
            ></span>
            <span class="testimonial-dot" onclick="showTestimonial(1)"></span>
            <span class="testimonial-dot" onclick="showTestimonial(2)"></span>
          </div>
        </div>
      </div>

      <!-- Newsletter -->
      <div class="newsletter-section">
        <h2 class="newsletter-title">📧 Đăng Ký Nhận Tin Khuyến Mãi</h2>
        <p class="newsletter-subtitle">
          Nhận mã giảm giá 10% ngay khi đăng ký và cập nhật các ưu đãi mới nhất!
        </p>
        <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
          <input
            type="email"
            class="newsletter-input"
            placeholder="Nhập email của bạn..."
            required
          />
          <button type="submit" class="newsletter-btn">Đăng Ký</button>
        </form>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>Về Chúng Tôi</h3>
          <p>
            Cửa hàng online cung cấp các sản phẩm thời trang chất lượng cao với
            giá cả hợp lý.
          </p>
          <p>
            Chúng tôi cam kết mang đến trải nghiệm mua sắm tốt nhất cho khách
            hàng.
          </p>
        </div>

        <div class="footer-section">
          <h3>Liên Hệ</h3>
          <p>Email: <EMAIL></p>
          <p>Điện thoại: 0123 456 789</p>
          <p>Địa chỉ: 123 Đường ABC, Quận XYZ, TP. HCM</p>
        </div>

        <div class="footer-section">
          <h3>Liên Kết Nhanh</h3>
          <a href="index.html">Trang Chủ</a>
          <a href="product.html">Sản Phẩm</a>
          <a href="cart.html">Giỏ Hàng</a>
          <a href="login.html">Đăng Nhập</a>
          <a href="register.html">Đăng Ký</a>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2023 Cửa Hàng Online. Tất cả quyền được bảo lưu.</p>
      </div>
    </footer>

    <!-- Games Modal -->
    <div class="game-modal" id="gamesModal">
      <div class="game-modal-content">
        <button class="game-close" onclick="closeGamesModal()">&times;</button>
        <h2
          style="
            text-align: center;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
          "
        >
          🎮 Mini Games - Chơi & Nhận Quà!
        </h2>

        <!-- Games Grid -->
        <div class="games-grid" id="gamesGrid">
          <div class="game-card" onclick="openGame('luckyWheel')">
            <div class="game-icon">🎡</div>
            <div class="game-title">Vòng Quay May Mắn</div>
            <div class="game-description">
              Quay để nhận mã giảm giá lên đến 20%!
            </div>
            <div class="game-status available" id="wheelStatus">
              Có thể chơi
            </div>
          </div>

          <div class="game-card" onclick="openGame('scratchCard')">
            <div class="game-icon">🎫</div>
            <div class="game-title">Thẻ Cào May Mắn</div>
            <div class="game-description">
              Cào để khám phá phần thưởng bí ẩn!
            </div>
            <div class="game-status available" id="scratchStatus">
              Có thể chơi
            </div>
          </div>

          <div class="game-card" onclick="openGame('dailyCheckin')">
            <div class="game-icon">📅</div>
            <div class="game-title">Check-in Hàng Ngày</div>
            <div class="game-description">
              Check-in mỗi ngày để tích điểm đổi quà!
            </div>
            <div class="game-status available" id="checkinStatus">
              Có thể chơi
            </div>
          </div>

          <div class="game-card" onclick="openGame('memoryGame')">
            <div class="game-icon">🧠</div>
            <div class="game-title">Trò Chơi Trí Nhớ</div>
            <div class="game-description">
              Tìm cặp sản phẩm giống nhau để nhận voucher!
            </div>
            <div class="game-status available" id="memoryStatus">
              Có thể chơi
            </div>
          </div>
        </div>

        <!-- Game Content Area -->
        <div
          id="gameContent"
          style="display: none; margin-top: var(--spacing-xl)"
        >
          <!-- Lucky Wheel -->
          <div id="luckyWheelGame" class="game-content" style="display: none">
            <div class="lucky-wheel-container">
              <h3
                style="
                  color: var(--primary-color);
                  margin-bottom: var(--spacing-lg);
                "
              >
                🎡 Vòng Quay May Mắn
              </h3>
              <p
                style="
                  margin-bottom: var(--spacing-lg);
                  color: var(--text-light);
                "
              >
                Quay bánh xe để nhận mã giảm giá! Mỗi ngày chỉ được quay 1 lần.
              </p>

              <div class="wheel-container">
                <div class="wheel-pointer"></div>
                <div class="wheel" id="wheel">
                  <div
                    class="wheel-segment"
                    style="background: #ff6b6b; transform: rotate(0deg)"
                  >
                    5% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #4ecdc4; transform: rotate(45deg)"
                  >
                    10% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #45b7d1; transform: rotate(90deg)"
                  >
                    15% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #96ceb4; transform: rotate(135deg)"
                  >
                    20% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #feca57; transform: rotate(180deg)"
                  >
                    Freeship
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #ff9ff3; transform: rotate(225deg)"
                  >
                    5% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #54a0ff; transform: rotate(270deg)"
                  >
                    10% OFF
                  </div>
                  <div
                    class="wheel-segment"
                    style="background: #5f27cd; transform: rotate(315deg)"
                  >
                    Thử lại
                  </div>
                </div>
              </div>

              <button class="spin-button" id="spinButton" onclick="spinWheel()">
                QUAY NGAY!
              </button>
              <p
                id="wheelResult"
                style="
                  margin-top: var(--spacing-md);
                  font-weight: 600;
                  color: var(--primary-color);
                "
              ></p>
            </div>
          </div>

          <!-- Scratch Card -->
          <div id="scratchCardGame" class="game-content" style="display: none">
            <div class="scratch-card-container">
              <h3
                style="
                  color: var(--primary-color);
                  margin-bottom: var(--spacing-lg);
                "
              >
                🎫 Thẻ Cào May Mắn
              </h3>
              <p
                style="
                  margin-bottom: var(--spacing-lg);
                  color: var(--text-light);
                "
              >
                Dùng chuột để cào và khám phá phần thưởng!
              </p>

              <div class="scratch-card" id="scratchCard">
                <div class="scratch-card-back" id="scratchReward">
                  🎁 Mã giảm 15%<br />SCRATCH15
                </div>
                <canvas
                  class="scratch-card-front"
                  id="scratchCanvas"
                  width="300"
                  height="200"
                >
                  Cào để mở quà!
                </canvas>
              </div>

              <button class="spin-button" onclick="resetScratchCard()">
                Thẻ Mới
              </button>
              <p
                id="scratchResult"
                style="
                  margin-top: var(--spacing-md);
                  font-weight: 600;
                  color: var(--primary-color);
                "
              ></p>
            </div>
          </div>

          <!-- Daily Check-in -->
          <div id="dailyCheckinGame" class="game-content" style="display: none">
            <div class="checkin-container">
              <h3
                style="
                  color: var(--primary-color);
                  margin-bottom: var(--spacing-lg);
                "
              >
                📅 Check-in Hàng Ngày
              </h3>

              <div class="checkin-stats">
                <div class="stat-item">
                  <div class="stat-value" id="totalPoints">0</div>
                  <div class="stat-label">Tổng Điểm</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value" id="currentStreak">0</div>
                  <div class="stat-label">Streak Hiện Tại</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value" id="maxStreak">0</div>
                  <div class="stat-label">Streak Cao Nhất</div>
                </div>
              </div>

              <div class="checkin-calendar" id="checkinCalendar">
                <!-- Calendar will be generated by JavaScript -->
              </div>

              <div style="text-align: center">
                <button
                  class="spin-button"
                  id="checkinButton"
                  onclick="dailyCheckin()"
                >
                  Check-in Hôm Nay
                </button>
                <p
                  id="checkinResult"
                  style="
                    margin-top: var(--spacing-md);
                    font-weight: 600;
                    color: var(--primary-color);
                  "
                ></p>
              </div>
            </div>
          </div>

          <!-- Memory Game -->
          <div
            id="memoryGameContent"
            class="game-content"
            style="display: none"
          >
            <div class="memory-game-container">
              <h3
                style="
                  color: var(--primary-color);
                  margin-bottom: var(--spacing-lg);
                "
              >
                🧠 Trò Chơi Trí Nhớ
              </h3>

              <!-- Difficulty Selector -->
              <div class="difficulty-selector">
                <button
                  class="difficulty-btn active"
                  onclick="setMemoryDifficulty('easy')"
                  data-difficulty="easy"
                >
                  Dễ (3x2)
                </button>
                <button
                  class="difficulty-btn"
                  onclick="setMemoryDifficulty('medium')"
                  data-difficulty="medium"
                >
                  Trung Bình (4x3)
                </button>
                <button
                  class="difficulty-btn"
                  onclick="setMemoryDifficulty('hard')"
                  data-difficulty="hard"
                >
                  Khó (4x4)
                </button>
              </div>

              <!-- Progress Bar -->
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  id="memoryProgress"
                  style="width: 0%"
                ></div>
              </div>

              <div class="memory-stats">
                <div class="memory-stat">
                  <div class="memory-stat-value" id="memoryMoves">0</div>
                  <div class="memory-stat-label">Lượt Chơi</div>
                </div>
                <div class="memory-stat">
                  <div class="memory-stat-value" id="memoryMatches">0</div>
                  <div class="memory-stat-label">Cặp Đúng</div>
                </div>
                <div class="memory-stat">
                  <div class="memory-stat-value" id="memoryTime">0</div>
                  <div class="memory-stat-label">Thời Gian (s)</div>
                </div>
                <div class="memory-stat">
                  <div class="memory-stat-value" id="memoryCombo">0</div>
                  <div class="memory-stat-label">Combo</div>
                </div>
              </div>

              <!-- Combo Display -->
              <div class="combo-display" id="comboDisplay">
                COMBO x<span id="comboMultiplier">1</span>
              </div>

              <div class="memory-grid" id="memoryGrid">
                <!-- Memory cards will be generated by JavaScript -->
              </div>

              <div style="text-align: center">
                <button class="spin-button" onclick="startMemoryGame()">
                  Chơi Mới
                </button>
                <button
                  class="spin-button"
                  onclick="showMemoryTutorial()"
                  style="background: var(--text-light); margin-left: 10px"
                >
                  Hướng Dẫn
                </button>
                <p
                  id="memoryResult"
                  style="
                    margin-top: var(--spacing-md);
                    font-weight: 600;
                    color: var(--primary-color);
                  "
                ></p>
              </div>
            </div>
          </div>
        </div>

        <div style="text-align: center; margin-top: var(--spacing-lg)">
          <button
            class="spin-button"
            onclick="closeGamesModal()"
            style="background: var(--text-light)"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>

    <script src="main.js"></script>
    <script src="game-engine.js"></script>
    <script>
      // Marketing Features JavaScript

      // 1. Promotion Banner Management
      function closePromoBanner() {
        const banner = document.getElementById("promoBanner");
        banner.style.display = "none";
        localStorage.setItem("promoBannerClosed", "true");
      }

      // Check if banner should be shown
      function initPromoBanner() {
        const bannerClosed = localStorage.getItem("promoBannerClosed");
        if (bannerClosed === "true") {
          document.getElementById("promoBanner").style.display = "none";
        }
      }

      // 2. Pop-up Modal Management
      let popupTimer;

      function showPopup() {
        const popup = document.getElementById("promoPopup");
        const dontShowAgain = localStorage.getItem("dontShowPopup");

        if (dontShowAgain !== "true") {
          popup.classList.add("show");
        }
      }

      function closePopup() {
        const popup = document.getElementById("promoPopup");
        const dontShowAgain = document.getElementById("dontShowAgain").checked;

        popup.classList.remove("show");

        if (dontShowAgain) {
          localStorage.setItem("dontShowPopup", "true");
        }
      }

      function claimDiscount() {
        // Kiểm tra xem hàm showNotification có tồn tại không
        if (typeof showNotification === "function") {
          showNotification(
            "🎉 Mã giảm giá SAVE15 đã được sao chép! Áp dụng khi thanh toán."
          );
        } else {
          alert(
            "🎉 Mã giảm giá SAVE15 đã được sao chép! Áp dụng khi thanh toán."
          );
        }

        // Copy discount code to clipboard
        if (navigator.clipboard) {
          navigator.clipboard.writeText("SAVE15").catch((err) => {
            console.warn("Could not copy to clipboard:", err);
          });
        }

        closePopup();
      }

      // 3. Countdown Timer
      function initCountdown() {
        // Set countdown end time (24 hours from now)
        const endTime = new Date().getTime() + 24 * 60 * 60 * 1000;

        function updateCountdown() {
          const now = new Date().getTime();
          const timeLeft = endTime - now;

          if (timeLeft > 0) {
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor(
              (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
            );
            const minutes = Math.floor(
              (timeLeft % (1000 * 60 * 60)) / (1000 * 60)
            );
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            document.getElementById("days").textContent = days
              .toString()
              .padStart(2, "0");
            document.getElementById("hours").textContent = hours
              .toString()
              .padStart(2, "0");
            document.getElementById("minutes").textContent = minutes
              .toString()
              .padStart(2, "0");
            document.getElementById("seconds").textContent = seconds
              .toString()
              .padStart(2, "0");
          } else {
            // Timer ended
            document.getElementById("days").textContent = "00";
            document.getElementById("hours").textContent = "00";
            document.getElementById("minutes").textContent = "00";
            document.getElementById("seconds").textContent = "00";
          }
        }

        updateCountdown();
        setInterval(updateCountdown, 1000);
      }

      // 4. Testimonials Slider
      let currentTestimonial = 0;
      const testimonials = document.querySelectorAll(".testimonial");
      const testimonialDots = document.querySelectorAll(".testimonial-dot");

      function showTestimonial(index) {
        // Kiểm tra xem có testimonials không
        if (testimonials.length === 0 || testimonialDots.length === 0) {
          return;
        }

        // Hide all testimonials
        testimonials.forEach((testimonial) => {
          testimonial.classList.remove("active");
        });

        // Remove active class from all dots
        testimonialDots.forEach((dot) => {
          dot.classList.remove("active");
        });

        // Show selected testimonial
        if (testimonials[index] && testimonialDots[index]) {
          testimonials[index].classList.add("active");
          testimonialDots[index].classList.add("active");
          currentTestimonial = index;
        }
      }

      function autoRotateTestimonials() {
        // Chỉ chạy nếu có testimonials
        if (testimonials.length > 0) {
          setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
            showTestimonial(currentTestimonial);
          }, 5000); // Change every 5 seconds
        }
      }

      // 5. Newsletter Subscription
      function subscribeNewsletter(event) {
        event.preventDefault();
        const email = event.target.querySelector(".newsletter-input").value;

        // Simulate API call
        setTimeout(() => {
          showNotification(
            "🎉 Đăng ký thành công! Mã giảm giá 10% đã được gửi đến email của bạn."
          );
          event.target.reset();
        }, 500);
      }

      // 6. Social Proof Notifications
      const socialProofData = [
        {
          name: "Nguyễn Thị Mai",
          product: "Áo thun nam",
          avatar:
            "https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3",
        },
        {
          name: "Trần Văn Nam",
          product: "Giày thể thao",
          avatar:
            "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3",
        },
        {
          name: "Lê Thị Hoa",
          product: "Túi xách nữ",
          avatar:
            "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3",
        },
        {
          name: "Phạm Minh Tuấn",
          product: "Áo khoác jean",
          avatar:
            "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3",
        },
        {
          name: "Hoàng Thị Lan",
          product: "Váy mùa hè",
          avatar:
            "https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=40&h=40&auto=format&fit=crop&ixlib=rb-4.0.3",
        },
      ];

      function showSocialProof() {
        const socialProof = document.getElementById("socialProof");
        const randomData =
          socialProofData[Math.floor(Math.random() * socialProofData.length)];

        // Update content
        socialProof.querySelector(".social-proof-avatar").src =
          randomData.avatar;
        socialProof.querySelector(".social-proof-name").textContent =
          randomData.name;
        socialProof.querySelector(
          ".social-proof-action"
        ).textContent = `vừa mua ${randomData.product}`;

        // Show notification
        socialProof.classList.add("show");

        // Hide after 4 seconds
        setTimeout(() => {
          socialProof.classList.remove("show");
        }, 4000);
      }

      function initSocialProof() {
        // Show first notification after 5 seconds
        setTimeout(showSocialProof, 5000);

        // Then show notifications every 20 seconds
        setInterval(showSocialProof, 20000);
      }

      // Mini Games JavaScript

      // Sound Toggle Function
      function toggleSound() {
        const soundEnabled = gameEngine.toggleSound();
        const soundToggle = document.getElementById("soundToggle");
        soundToggle.textContent = soundEnabled ? "🔊" : "🔇";
        soundToggle.classList.toggle("muted", !soundEnabled);

        gameEngine.playSound("click");
        showNotification(soundEnabled ? "Âm thanh đã bật" : "Âm thanh đã tắt");
      }

      // Game Modal Management
      function openGamesModal() {
        const modal = document.getElementById("gamesModal");
        modal.classList.add("show");
        updateGameStatuses();
        gameEngine.playSound("click");
      }

      function closeGamesModal() {
        const modal = document.getElementById("gamesModal");
        modal.classList.remove("show");

        // Hide all game content
        document.getElementById("gameContent").style.display = "none";
        document.querySelectorAll(".game-content").forEach((game) => {
          game.style.display = "none";
        });

        // Show games grid
        document.getElementById("gamesGrid").style.display = "grid";
      }

      function openGame(gameType) {
        // Hide games grid
        document.getElementById("gamesGrid").style.display = "none";

        // Show game content area
        document.getElementById("gameContent").style.display = "block";

        // Hide all games
        document.querySelectorAll(".game-content").forEach((game) => {
          game.style.display = "none";
        });

        // Show selected game
        switch (gameType) {
          case "luckyWheel":
            document.getElementById("luckyWheelGame").style.display = "block";
            initLuckyWheel();
            break;
          case "scratchCard":
            document.getElementById("scratchCardGame").style.display = "block";
            initScratchCard();
            break;
          case "dailyCheckin":
            document.getElementById("dailyCheckinGame").style.display = "block";
            initDailyCheckin();
            break;
          case "memoryGame":
            document.getElementById("memoryGameContent").style.display =
              "block";
            initMemoryGame();
            break;
        }
      }

      function updateGameStatuses() {
        const today = new Date().toDateString();

        // Lucky Wheel status
        const wheelPlayed = localStorage.getItem("wheelPlayed_" + today);
        const wheelStatus = document.getElementById("wheelStatus");
        if (wheelPlayed) {
          wheelStatus.textContent = "Đã chơi hôm nay";
          wheelStatus.className = "game-status played";
        } else {
          wheelStatus.textContent = "Có thể chơi";
          wheelStatus.className = "game-status available";
        }

        // Scratch Card status
        const scratchPlayed = localStorage.getItem("scratchPlayed_" + today);
        const scratchStatus = document.getElementById("scratchStatus");
        if (scratchPlayed) {
          scratchStatus.textContent = "Đã chơi hôm nay";
          scratchStatus.className = "game-status played";
        } else {
          scratchStatus.textContent = "Có thể chơi";
          scratchStatus.className = "game-status available";
        }

        // Check-in always available
        const checkinStatus = document.getElementById("checkinStatus");
        checkinStatus.textContent = "Có thể chơi";
        checkinStatus.className = "game-status available";

        // Memory game always available
        const memoryStatus = document.getElementById("memoryStatus");
        memoryStatus.textContent = "Có thể chơi";
        memoryStatus.className = "game-status available";
      }

      // 1. Lucky Wheel Game
      let wheelSpinning = false;

      function initLuckyWheel() {
        const today = new Date().toDateString();
        const wheelPlayed = localStorage.getItem("wheelPlayed_" + today);
        const spinButton = document.getElementById("spinButton");

        if (wheelPlayed) {
          spinButton.disabled = true;
          spinButton.textContent = "Đã quay hôm nay";
          document.getElementById("wheelResult").textContent =
            "Quay lại vào ngày mai!";
        } else {
          spinButton.disabled = false;
          spinButton.textContent = "QUAY NGAY!";
          document.getElementById("wheelResult").textContent = "";
        }
      }

      function spinWheel() {
        if (wheelSpinning) return;

        const today = new Date().toDateString();
        const wheelPlayed = localStorage.getItem("wheelPlayed_" + today);

        if (wheelPlayed) {
          showNotification("Bạn đã quay hôm nay rồi! Quay lại vào ngày mai.");
          gameEngine.playSound("lose");
          return;
        }

        // Anti-cheat validation
        if (!gameEngine.validateGameState("wheel", {})) {
          showNotification("Vui lòng chờ một chút trước khi quay lại!");
          return;
        }

        wheelSpinning = true;
        const wheel = document.getElementById("wheel");
        const spinButton = document.getElementById("spinButton");
        const resultElement = document.getElementById("wheelResult");

        spinButton.disabled = true;
        spinButton.textContent = "Đang quay...";
        spinButton.innerHTML =
          '<span class="loading-spinner"></span> Đang quay...';

        // Enhanced random with better distribution
        const weights = [15, 20, 15, 10, 15, 15, 8, 2]; // Lower chance for best prizes
        const segmentIndex = gameEngine.weightedRandom(weights);
        const randomRotation =
          gameEngine.secureRandom(1440, 2160) + segmentIndex * 45;

        wheel.classList.add("spinning");
        wheel.style.transform = `rotate(${randomRotation}deg)`;
        gameEngine.playSound("spin");
        gameEngine.vibrate([100, 50, 100]);

        setTimeout(() => {
          wheelSpinning = false;
          wheel.classList.remove("spinning");

          const prizes = [
            { text: "5% OFF", code: "WHEEL5", value: 5 },
            { text: "10% OFF", code: "WHEEL10", value: 10 },
            { text: "15% OFF", code: "WHEEL15", value: 15 },
            { text: "20% OFF", code: "WHEEL20", value: 20 },
            { text: "Freeship", code: "FREESHIP", value: 15 },
            { text: "5% OFF", code: "WHEEL5B", value: 5 },
            { text: "10% OFF", code: "WHEEL10B", value: 10 },
            { text: "Thử lại", code: null, value: 0 },
          ];

          const prize = prizes[segmentIndex];

          if (prize.code) {
            resultElement.innerHTML = `🎉 Chúc mừng! Bạn nhận được: <strong>${prize.text}</strong><br>Mã: <strong>${prize.code}</strong>`;
            showNotification(
              `🎉 Bạn nhận được ${prize.text}! Mã: ${prize.code}`
            );

            // Copy to clipboard
            if (navigator.clipboard) {
              navigator.clipboard.writeText(prize.code);
            }

            gameEngine.playSound("win");
            gameEngine.vibrate([200, 100, 200, 100, 200]);
            gameEngine.createConfetti();

            // Update stats and check achievements
            gameEngine.updateStats("wheel", "spins");
            gameEngine.updateStats("wheel", "wins");

            if (prize.value >= 20) {
              gameEngine.checkAchievement("luckyStreak");
            }
          } else {
            resultElement.innerHTML = "😅 Chúc bạn may mắn lần sau!";
            showNotification("😅 Chúc bạn may mắn lần sau!");
            gameEngine.playSound("lose");
            gameEngine.updateStats("wheel", "spins");
          }

          // Mark as played today
          localStorage.setItem("wheelPlayed_" + today, "true");
          spinButton.innerHTML = "Đã quay hôm nay";
        }, 4000);
      }

      // 2. Scratch Card Game
      let scratchCanvas, scratchCtx;

      function initScratchCard() {
        const today = new Date().toDateString();
        const scratchPlayed = localStorage.getItem("scratchPlayed_" + today);

        if (scratchPlayed) {
          document.getElementById("scratchResult").textContent =
            "Đã cào hôm nay! Quay lại vào ngày mai.";
          return;
        }

        scratchCanvas = document.getElementById("scratchCanvas");
        scratchCtx = scratchCanvas.getContext("2d");

        // Draw scratch surface
        scratchCtx.fillStyle = "#c0c0c0";
        scratchCtx.fillRect(0, 0, 300, 200);

        // Add text
        scratchCtx.fillStyle = "white";
        scratchCtx.font = "20px Arial";
        scratchCtx.textAlign = "center";
        scratchCtx.fillText("Cào để mở quà!", 150, 100);

        // Set up scratching
        scratchCtx.globalCompositeOperation = "destination-out";

        let isScratching = false;
        let scratchedArea = 0;

        function startScratch(e) {
          isScratching = true;
          scratch(e);
        }

        function scratch(e) {
          if (!isScratching) return;

          const rect = scratchCanvas.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;

          scratchCtx.beginPath();
          scratchCtx.arc(x, y, 20, 0, 2 * Math.PI);
          scratchCtx.fill();

          // Check scratched area
          scratchedArea += 1;
          if (scratchedArea > 50) {
            revealPrize();
          }
        }

        function stopScratch() {
          isScratching = false;
        }

        function revealPrize() {
          scratchCanvas.style.opacity = "0";

          const prizes = [
            { text: "Mã giảm 15%", code: "SCRATCH15" },
            { text: "Freeship", code: "FREESHIP" },
            { text: "Mã giảm 10%", code: "SCRATCH10" },
            { text: "Thử lại", code: null },
          ];

          const prize = prizes[Math.floor(Math.random() * prizes.length)];

          if (prize.code) {
            document.getElementById(
              "scratchReward"
            ).innerHTML = `🎁 ${prize.text}<br>${prize.code}`;
            document.getElementById(
              "scratchResult"
            ).innerHTML = `🎉 Chúc mừng! Bạn nhận được: <strong>${prize.text}</strong><br>Mã: <strong>${prize.code}</strong>`;
            showNotification(
              `🎉 Bạn nhận được ${prize.text}! Mã: ${prize.code}`
            );

            if (navigator.clipboard) {
              navigator.clipboard.writeText(prize.code);
            }

            gameEngine.playSound("win");
            gameEngine.vibrate([200, 100, 200]);
            gameEngine.updateStats("scratch", "plays");
            gameEngine.updateStats("scratch", "wins");
          } else {
            document.getElementById("scratchReward").innerHTML =
              "😅 Chúc bạn<br>may mắn lần sau!";
            document.getElementById("scratchResult").textContent =
              "😅 Chúc bạn may mắn lần sau!";
            showNotification("😅 Chúc bạn may mắn lần sau!");
            gameEngine.playSound("lose");
            gameEngine.updateStats("scratch", "plays");
          }

          localStorage.setItem("scratchPlayed_" + today, "true");
        }

        // Mouse events
        scratchCanvas.addEventListener("mousedown", startScratch);
        scratchCanvas.addEventListener("mousemove", scratch);
        scratchCanvas.addEventListener("mouseup", stopScratch);

        // Touch events
        scratchCanvas.addEventListener("touchstart", (e) => {
          e.preventDefault();
          startScratch(e.touches[0]);
        });
        scratchCanvas.addEventListener("touchmove", (e) => {
          e.preventDefault();
          scratch(e.touches[0]);
        });
        scratchCanvas.addEventListener("touchend", stopScratch);
      }

      function resetScratchCard() {
        const today = new Date().toDateString();
        localStorage.removeItem("scratchPlayed_" + today);
        document.getElementById("scratchResult").textContent = "";
        document.getElementById("scratchCanvas").style.opacity = "1";
        initScratchCard();
      }

      // 3. Daily Check-in Game
      function initDailyCheckin() {
        loadCheckinData();
        generateCheckinCalendar();
        updateCheckinStats();
      }

      function loadCheckinData() {
        const defaultData = {
          totalPoints: 0,
          currentStreak: 0,
          maxStreak: 0,
          checkinDates: [],
        };

        const saved = localStorage.getItem("checkinData");
        window.checkinData = saved ? JSON.parse(saved) : defaultData;
      }

      function saveCheckinData() {
        localStorage.setItem("checkinData", JSON.stringify(window.checkinData));
      }

      function generateCheckinCalendar() {
        const calendar = document.getElementById("checkinCalendar");
        calendar.innerHTML = "";

        const today = new Date();
        const currentMonth = today.getMonth();
        const currentYear = today.getFullYear();

        // Get first day of month and number of days
        const firstDay = new Date(currentYear, currentMonth, 1).getDay();
        const daysInMonth = new Date(
          currentYear,
          currentMonth + 1,
          0
        ).getDate();

        // Add empty cells for days before month starts
        for (let i = 0; i < firstDay; i++) {
          const emptyDay = document.createElement("div");
          calendar.appendChild(emptyDay);
        }

        // Add days of month
        for (let day = 1; day <= daysInMonth; day++) {
          const dayElement = document.createElement("div");
          dayElement.className = "checkin-day";

          const dayNumber = document.createElement("div");
          dayNumber.className = "day-number";
          dayNumber.textContent = day;

          const dayReward = document.createElement("div");
          dayReward.className = "day-reward";
          dayReward.textContent = `+${(day % 7) + 1}pt`;

          dayElement.appendChild(dayNumber);
          dayElement.appendChild(dayReward);

          const dayDate = new Date(
            currentYear,
            currentMonth,
            day
          ).toDateString();
          const todayDate = today.toDateString();

          if (window.checkinData.checkinDates.includes(dayDate)) {
            dayElement.classList.add("checked");
          } else if (dayDate === todayDate) {
            dayElement.classList.add("today");
          } else if (new Date(dayDate) > today) {
            dayElement.classList.add("future");
          }

          calendar.appendChild(dayElement);
        }
      }

      function updateCheckinStats() {
        document.getElementById("totalPoints").textContent =
          window.checkinData.totalPoints;
        document.getElementById("currentStreak").textContent =
          window.checkinData.currentStreak;
        document.getElementById("maxStreak").textContent =
          window.checkinData.maxStreak;
      }

      function dailyCheckin() {
        const today = new Date().toDateString();

        if (window.checkinData.checkinDates.includes(today)) {
          showNotification("Bạn đã check-in hôm nay rồi!");
          return;
        }

        // Add points
        const points = (new Date().getDate() % 7) + 1;
        window.checkinData.totalPoints += points;

        // Update streak
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayDate = yesterday.toDateString();

        if (window.checkinData.checkinDates.includes(yesterdayDate)) {
          window.checkinData.currentStreak += 1;
        } else {
          window.checkinData.currentStreak = 1;
        }

        if (window.checkinData.currentStreak > window.checkinData.maxStreak) {
          window.checkinData.maxStreak = window.checkinData.currentStreak;
        }

        // Add today to checked dates
        window.checkinData.checkinDates.push(today);

        saveCheckinData();
        generateCheckinCalendar();
        updateCheckinStats();

        document.getElementById(
          "checkinResult"
        ).textContent = `🎉 Check-in thành công! +${points} điểm`;
        showNotification(`🎉 Check-in thành công! +${points} điểm`);

        gameEngine.playSound("win");
        gameEngine.vibrate([100, 50, 100]);
        gameEngine.updateStats("checkin", "totalPoints", points);
        gameEngine.updateStats(
          "checkin",
          "streak",
          window.checkinData.currentStreak
        );

        // Check achievements
        if (window.checkinData.currentStreak >= 7) {
          gameEngine.checkAchievement("dailyPlayer");
        }
      }

      // 4. Memory Game
      let memoryCards = [];
      let flippedCards = [];
      let matchedPairs = 0;
      let moves = 0;
      let gameTimer = 0;
      let timerInterval;
      let memoryDifficulty = "easy";
      let comboCount = 0;
      let comboMultiplier = 1;

      const difficultySettings = {
        easy: { rows: 2, cols: 3, pairs: 3, symbols: ["👕", "👖", "👗"] },
        medium: {
          rows: 3,
          cols: 4,
          pairs: 6,
          symbols: ["👕", "👖", "👗", "👠", "👜", "⌚"],
        },
        hard: {
          rows: 4,
          cols: 4,
          pairs: 8,
          symbols: ["👕", "👖", "👗", "👠", "👜", "⌚", "🕶️", "🧢"],
        },
      };

      function setMemoryDifficulty(difficulty) {
        memoryDifficulty = difficulty;

        // Update button states
        document.querySelectorAll(".difficulty-btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        document
          .querySelector(`[data-difficulty="${difficulty}"]`)
          .classList.add("active");

        // Update grid layout
        const settings = difficultySettings[difficulty];
        const grid = document.getElementById("memoryGrid");
        grid.style.gridTemplateColumns = `repeat(${settings.cols}, 1fr)`;

        gameEngine.playSound("click");

        // Restart game with new difficulty
        if (memoryCards.length > 0) {
          startMemoryGame();
        }
      }

      function showMemoryTutorial() {
        gameEngine.showTutorial("memoryGame");
      }

      function initMemoryGame() {
        resetMemoryGame();
        generateMemoryCards();
      }

      function resetMemoryGame() {
        matchedPairs = 0;
        moves = 0;
        gameTimer = 0;
        flippedCards = [];
        comboCount = 0;
        comboMultiplier = 1;

        if (timerInterval) {
          clearInterval(timerInterval);
        }

        updateMemoryStats();
        updateComboDisplay();
        gameEngine.updateProgress("memoryGame", 0);
        document.getElementById("memoryResult").textContent = "";
      }

      function generateMemoryCards() {
        const grid = document.getElementById("memoryGrid");
        grid.innerHTML = "";

        const settings = difficultySettings[memoryDifficulty];
        const symbols = settings.symbols;

        // Create pairs
        memoryCards = [...symbols, ...symbols];

        // Enhanced shuffle using Fisher-Yates algorithm
        for (let i = memoryCards.length - 1; i > 0; i--) {
          const j = gameEngine.secureRandom(0, i);
          [memoryCards[i], memoryCards[j]] = [memoryCards[j], memoryCards[i]];
        }

        // Create card elements
        memoryCards.forEach((symbol, index) => {
          const card = document.createElement("div");
          card.className = "memory-card";
          card.dataset.index = index;
          card.dataset.symbol = symbol;
          card.setAttribute("tabindex", "0");
          card.setAttribute("role", "button");
          card.setAttribute("aria-label", `Memory card ${index + 1}`);

          const front = document.createElement("div");
          front.className = "memory-card-face memory-card-front";
          front.textContent = "?";

          const back = document.createElement("div");
          back.className = "memory-card-face memory-card-back";
          back.textContent = symbol;

          card.appendChild(front);
          card.appendChild(back);

          // Add click and keyboard event listeners
          card.addEventListener("click", () => flipCard(card, index));
          card.addEventListener("keydown", (e) => {
            if (e.key === "Enter" || e.key === " ") {
              e.preventDefault();
              flipCard(card, index);
            }
          });

          grid.appendChild(card);
        });
      }

      function updateComboDisplay() {
        const comboDisplay = document.getElementById("comboDisplay");
        const multiplierSpan = document.getElementById("comboMultiplier");

        if (comboCount > 1) {
          comboDisplay.classList.add("active");
          multiplierSpan.textContent = comboMultiplier;
        } else {
          comboDisplay.classList.remove("active");
        }

        document.getElementById("memoryCombo").textContent = comboCount;
      }

      function flipCard(card, index) {
        if (
          flippedCards.length >= 2 ||
          card.classList.contains("flipped") ||
          card.classList.contains("matched")
        ) {
          return;
        }

        // Start timer on first move
        if (moves === 0) {
          startMemoryTimer();
        }

        card.classList.add("flipped");
        flippedCards.push({ card, index, symbol: card.dataset.symbol });

        gameEngine.playSound("click");
        gameEngine.vibrate([50]);

        if (flippedCards.length === 2) {
          moves++;
          updateMemoryStats();

          setTimeout(() => {
            checkMemoryMatch();
          }, 1000);
        }
      }

      function checkMemoryMatch() {
        const [card1, card2] = flippedCards;
        const settings = difficultySettings[memoryDifficulty];

        if (card1.symbol === card2.symbol) {
          // Match found
          card1.card.classList.add("matched");
          card2.card.classList.add("matched");
          matchedPairs++;

          // Update combo system
          comboCount++;
          comboMultiplier = Math.min(comboCount, 5); // Max 5x multiplier
          updateComboDisplay();

          gameEngine.playSound("match");
          gameEngine.vibrate([100, 50, 100]);

          // Update progress
          const progress = (matchedPairs / settings.pairs) * 100;
          gameEngine.updateProgress("memoryGame", progress);

          if (matchedPairs === settings.pairs) {
            // Game completed
            clearInterval(timerInterval);
            const resultElement = document.getElementById("memoryResult");

            // Calculate bonus based on performance
            let bonus = "";
            let rewardCode = "MEMORY10";

            if (gameTimer < 30 && moves < 20) {
              bonus = " + Bonus 5%";
              rewardCode = "MEMORY15";
              gameEngine.checkAchievement("memoryChamp");
            }

            if (moves < 20) {
              gameEngine.checkAchievement("speedster");
            }

            resultElement.innerHTML = `🎉 Chúc mừng! Hoàn thành trong ${moves} lượt và ${gameTimer} giây!<br><strong>Phần thưởng: Mã giảm 10%${bonus} - ${rewardCode}</strong>`;
            showNotification(
              `🎉 Hoàn thành Memory Game! Nhận mã ${rewardCode}`
            );

            if (navigator.clipboard) {
              navigator.clipboard.writeText(rewardCode);
            }

            gameEngine.playSound("win");
            gameEngine.createConfetti();
            gameEngine.updateStats("memory", "completions");
            gameEngine.updateStats("memory", "totalMoves", moves);
          }
        } else {
          // No match - reset combo
          comboCount = 0;
          comboMultiplier = 1;
          updateComboDisplay();

          card1.card.classList.remove("flipped");
          card2.card.classList.remove("flipped");

          // Add shake animation for wrong match
          card1.card.style.animation = "shake 0.5s ease-in-out";
          card2.card.style.animation = "shake 0.5s ease-in-out";

          setTimeout(() => {
            card1.card.style.animation = "";
            card2.card.style.animation = "";
          }, 500);
        }

        flippedCards = [];
        updateMemoryStats();
      }

      function startMemoryTimer() {
        timerInterval = setInterval(() => {
          gameTimer++;
          updateMemoryStats();
        }, 1000);
      }

      function updateMemoryStats() {
        document.getElementById("memoryMoves").textContent = moves;
        document.getElementById("memoryMatches").textContent = matchedPairs;
        document.getElementById("memoryTime").textContent = gameTimer;
      }

      function startMemoryGame() {
        resetMemoryGame();
        generateMemoryCards();
      }

      // Initialize all marketing features
      document.addEventListener("DOMContentLoaded", function () {
        initPromoBanner();
        initCountdown();
        autoRotateTestimonials();
        initSocialProof();

        // Show popup after 12 seconds
        setTimeout(showPopup, 12000);
      });
    </script>
  </body>
</html>
