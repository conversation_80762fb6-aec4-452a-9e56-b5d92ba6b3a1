// Enhanced Game Engine Module
class GameEngine {
  constructor() {
    this.soundEnabled = localStorage.getItem('soundEnabled') !== 'false';
    this.achievements = JSON.parse(localStorage.getItem('achievements') || '[]');
    this.userStats = JSON.parse(localStorage.getItem('userStats') || '{}');
    this.initializeSounds();
    this.initializeAchievements();
  }

  // Sound System
  initializeSounds() {
    this.sounds = {
      click: this.createSound(800, 0.1, 'square'),
      win: this.createSound([523, 659, 784, 1047], 0.3, 'sine'),
      lose: this.createSound([200, 150, 100], 0.2, 'sawtooth'),
      spin: this.createSound([400, 450, 500], 0.15, 'sine'),
      match: this.createSound([659, 784], 0.2, 'sine'),
      scratch: this.createSound([300, 350], 0.1, 'triangle'),
      achievement: this.createSound([523, 659, 784, 1047, 1319], 0.4, 'sine')
    };
  }

  createSound(frequencies, duration, type = 'sine') {
    return () => {
      if (!this.soundEnabled) return;
      
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const freqArray = Array.isArray(frequencies) ? frequencies : [frequencies];
      
      freqArray.forEach((freq, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
        oscillator.type = type;
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime + index * 0.1);
        oscillator.stop(audioContext.currentTime + duration + index * 0.1);
      });
    };
  }

  playSound(soundName) {
    if (this.sounds[soundName]) {
      this.sounds[soundName]();
    }
  }

  toggleSound() {
    this.soundEnabled = !this.soundEnabled;
    localStorage.setItem('soundEnabled', this.soundEnabled);
    return this.soundEnabled;
  }

  // Haptic Feedback
  vibrate(pattern = [100]) {
    if (navigator.vibrate) {
      navigator.vibrate(pattern);
    }
  }

  // Achievement System
  initializeAchievements() {
    this.achievementDefinitions = {
      firstSpin: { name: 'First Spin', description: 'Spin the wheel for the first time', icon: '🎡' },
      luckyStreak: { name: 'Lucky Streak', description: 'Win 3 times in a row', icon: '🍀' },
      scratchMaster: { name: 'Scratch Master', description: 'Play scratch card 10 times', icon: '🎫' },
      memoryChamp: { name: 'Memory Champion', description: 'Complete memory game in under 30 seconds', icon: '🧠' },
      dailyPlayer: { name: 'Daily Player', description: 'Check-in for 7 consecutive days', icon: '📅' },
      pointCollector: { name: 'Point Collector', description: 'Collect 100 points', icon: '💎' },
      speedster: { name: 'Speedster', description: 'Complete memory game in under 20 moves', icon: '⚡' },
      persistent: { name: 'Persistent', description: 'Play games for 30 days', icon: '🏆' }
    };
  }

  checkAchievement(achievementId, condition = true) {
    if (!condition || this.achievements.includes(achievementId)) return false;
    
    this.achievements.push(achievementId);
    localStorage.setItem('achievements', JSON.stringify(this.achievements));
    
    const achievement = this.achievementDefinitions[achievementId];
    if (achievement) {
      this.showAchievementNotification(achievement);
      this.playSound('achievement');
      this.vibrate([200, 100, 200]);
    }
    
    return true;
  }

  showAchievementNotification(achievement) {
    const notification = document.createElement('div');
    notification.className = 'achievement-notification';
    notification.innerHTML = `
      <div style="font-size: 3rem; margin-bottom: 10px;">${achievement.icon}</div>
      <h3 style="color: var(--primary-color); margin-bottom: 5px;">Achievement Unlocked!</h3>
      <h4 style="margin-bottom: 5px;">${achievement.name}</h4>
      <p style="color: var(--text-light); margin: 0;">${achievement.description}</p>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  // Statistics Tracking
  updateStats(category, action, value = 1) {
    if (!this.userStats[category]) {
      this.userStats[category] = {};
    }
    
    if (!this.userStats[category][action]) {
      this.userStats[category][action] = 0;
    }
    
    this.userStats[category][action] += value;
    localStorage.setItem('userStats', JSON.stringify(this.userStats));
    
    // Check for achievements based on stats
    this.checkStatBasedAchievements(category, action);
  }

  checkStatBasedAchievements(category, action) {
    const stats = this.userStats[category];
    
    switch (category) {
      case 'wheel':
        if (action === 'spins' && stats.spins === 1) {
          this.checkAchievement('firstSpin');
        }
        break;
      case 'scratch':
        if (action === 'plays' && stats.plays >= 10) {
          this.checkAchievement('scratchMaster');
        }
        break;
      case 'checkin':
        if (action === 'totalPoints' && stats.totalPoints >= 100) {
          this.checkAchievement('pointCollector');
        }
        break;
    }
  }

  // Anti-cheat measures
  validateGameState(gameType, data) {
    const now = Date.now();
    const lastPlay = localStorage.getItem(`lastPlay_${gameType}`);
    
    // Rate limiting
    if (lastPlay && now - parseInt(lastPlay) < 1000) {
      return false;
    }
    
    localStorage.setItem(`lastPlay_${gameType}`, now.toString());
    return true;
  }

  // Confetti Effect
  createConfetti() {
    const container = document.createElement('div');
    container.className = 'confetti-container';
    document.body.appendChild(container);
    
    const colors = ['var(--primary-color)', 'var(--secondary-color)', 'var(--accent-color)'];
    
    for (let i = 0; i < 50; i++) {
      const confetti = document.createElement('div');
      confetti.className = 'confetti-piece';
      confetti.style.left = Math.random() * 100 + '%';
      confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
      confetti.style.animationDelay = Math.random() * 3 + 's';
      confetti.style.animationDuration = (Math.random() * 2 + 2) + 's';
      container.appendChild(confetti);
    }
    
    setTimeout(() => container.remove(), 5000);
  }

  // Progress tracking
  updateProgress(gameType, progress) {
    const progressBar = document.querySelector(`#${gameType} .progress-bar .progress-fill`);
    if (progressBar) {
      progressBar.style.width = progress + '%';
    }
  }

  // Random number generation with better distribution
  secureRandom(min, max) {
    const array = new Uint32Array(1);
    crypto.getRandomValues(array);
    return min + (array[0] % (max - min + 1));
  }

  // Weighted random for fair gameplay
  weightedRandom(weights) {
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return i;
      }
    }
    
    return weights.length - 1;
  }

  // Tutorial system
  showTutorial(gameType) {
    const tutorials = {
      luckyWheel: [
        { title: 'Welcome to Lucky Wheel!', content: 'Click the spin button to try your luck!' },
        { title: 'Daily Limit', content: 'You can spin once per day. Come back tomorrow for another chance!' },
        { title: 'Prizes', content: 'Win discount codes, free shipping, or try again!' }
      ],
      scratchCard: [
        { title: 'Scratch Card Game', content: 'Use your mouse or finger to scratch the silver surface!' },
        { title: 'Reveal Prizes', content: 'Scratch enough area to reveal your prize underneath!' },
        { title: 'Daily Play', content: 'One scratch card per day - make it count!' }
      ],
      memoryGame: [
        { title: 'Memory Challenge', content: 'Click cards to flip them and find matching pairs!' },
        { title: 'Strategy', content: 'Remember the positions of cards you\'ve seen!' },
        { title: 'Win Condition', content: 'Match all pairs to win a discount code!' }
      ],
      dailyCheckin: [
        { title: 'Daily Check-in', content: 'Check in every day to earn points!' },
        { title: 'Streak Bonus', content: 'Consecutive days give you streak bonuses!' },
        { title: 'Redeem Points', content: 'Use points to get exclusive rewards!' }
      ]
    };
    
    const tutorial = tutorials[gameType];
    if (!tutorial) return;
    
    this.createTutorialOverlay(tutorial);
  }

  createTutorialOverlay(steps) {
    const overlay = document.createElement('div');
    overlay.className = 'tutorial-overlay';
    
    const content = document.createElement('div');
    content.className = 'tutorial-content';
    
    let currentStep = 0;
    
    const renderStep = () => {
      const step = steps[currentStep];
      content.innerHTML = `
        <h3>${step.title}</h3>
        <p>${step.content}</p>
        <div class="tutorial-navigation">
          <button onclick="this.closest('.tutorial-overlay').remove()" 
                  style="background: var(--text-light); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            Skip
          </button>
          <div>
            <span>${currentStep + 1} / ${steps.length}</span>
            <button onclick="nextStep()" 
                    style="background: var(--primary-color); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
              ${currentStep === steps.length - 1 ? 'Finish' : 'Next'}
            </button>
          </div>
        </div>
      `;
    };
    
    window.nextStep = () => {
      if (currentStep < steps.length - 1) {
        currentStep++;
        renderStep();
      } else {
        overlay.remove();
        delete window.nextStep;
      }
    };
    
    renderStep();
    overlay.appendChild(content);
    document.body.appendChild(overlay);
  }

  // Utility methods
  formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  formatNumber(num) {
    return new Intl.NumberFormat().format(num);
  }

  // Error handling
  handleError(error, context) {
    console.error(`Game Engine Error in ${context}:`, error);
    
    // Show user-friendly error message
    if (typeof showNotification === 'function') {
      showNotification('Đã xảy ra lỗi. Vui lòng thử lại!');
    }
  }
}

// Initialize global game engine
window.gameEngine = new GameEngine();
